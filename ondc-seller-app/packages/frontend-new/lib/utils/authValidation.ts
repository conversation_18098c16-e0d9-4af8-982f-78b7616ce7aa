/**
 * Utility functions for authentication validation
 */

/**
 * Checks if the input is a valid email address
 */
export const isValidEmail = (value: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
};

/**
 * Checks if the input is a valid mobile number (91 followed by 10 digits)
 */
export const isValidMobileNumber = (value: string): boolean => {
  const mobileRegex = /^91\d{10}$/;
  return mobileRegex.test(value);
};

/**
 * Checks if the input is either a valid email or mobile number
 */
export const isValidEmailOrMobile = (value: string): boolean => {
  return isValidEmail(value) || isValidMobileNumber(value);
};

/**
 * Determines the type of login credential
 */
export const getLoginCredentialType = (value: string): 'email' | 'mobile' | 'invalid' => {
  if (isValidEmail(value)) {
    return 'email';
  }
  if (isValidMobileNumber(value)) {
    return 'mobile';
  }
  return 'invalid';
};

/**
 * Formats mobile number for display (adds spaces for readability)
 */
export const formatMobileNumber = (mobile: string): string => {
  if (mobile.length === 12 && mobile.startsWith('91')) {
    return `+91 ${mobile.slice(2, 7)} ${mobile.slice(7)}`;
  }
  return mobile;
};

/**
 * Validates mobile number format and provides specific error messages
 */
export const validateMobileNumber = (mobile: string): { isValid: boolean; error?: string } => {
  if (!mobile) {
    return { isValid: false, error: 'Mobile number is required' };
  }
  
  if (!mobile.startsWith('91')) {
    return { isValid: false, error: 'Mobile number must start with 91' };
  }
  
  if (mobile.length !== 12) {
    return { isValid: false, error: 'Mobile number must be 12 digits (91 + 10 digits)' };
  }
  
  if (!/^\d+$/.test(mobile)) {
    return { isValid: false, error: 'Mobile number must contain only digits' };
  }
  
  return { isValid: true };
};