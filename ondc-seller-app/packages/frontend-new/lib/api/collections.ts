import { medusaClient } from './medusa-client';
import { getAuthHeaders } from '@/lib/utils/tokenUtils';

export interface Collection {
  id: string;
  name: string;
  slug?: string;
  description?: string;
  image?: string;
  status: 'active' | 'inactive';
  sort_order?: number;
  createdAt: string;
  updatedAt?: string;
  products_count?: number;
  products?: any[];
  conditions?: any;
  type?: 'manual' | 'automatic';
}

export interface CollectionsResponse {
  collections: Collection[];
  count: number;
  limit: number;
  offset: number;
}

export const collectionsApi = {
  getCollections: async (storeHandle: string, params?: {
    limit?: number;
    offset?: number;
    search?: string;
    status?: string;
    type?: string;
  }): Promise<CollectionsResponse> => {
    console.log('=== COLLECTIONS API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Request params:', params);
    
    try {
      const response = await medusaClient.admin.get('/collections', {
        headers: getAuthHeaders(storeHandle, {}, true), // true = use medusa_token for admin APIs
        params,
      });
      
      console.log('Collections API response status:', response.status);
      console.log('Collections API response headers:', response.headers);
      console.log('Collections API response data:', response.data);
      console.log('Collections API response structure:', {
        dataType: typeof response.data,
        dataKeys: response.data ? Object.keys(response.data) : 'No data',
        collectionsArray: Array.isArray(response.data?.collections),
        collectionsCount: response.data?.collections?.length || 0,
        sampleCollection: response.data?.collections?.[0] || 'No collections'
      });
      
      return response.data;
    } catch (error: any) {
      console.error('=== COLLECTIONS API ERROR ===');
      console.error('Error details:', error);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
        console.error('Error response headers:', error.response.headers);
      }
      
      throw error;
    }
  },

  getCollection: async (storeHandle: string, collectionId: string): Promise<Collection> => {
    console.log('=== GET COLLECTION API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Collection ID:', collectionId);
    
    try {
      const response = await medusaClient.admin.get(`/collections/${collectionId}`, {
        headers: getAuthHeaders(storeHandle, {}, true), // true = use medusa_token for admin APIs
      });
      
      console.log('Get collection API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching collection:', error);
      throw error;
    }
  },

  createCollection: async (storeHandle: string, collectionData: Partial<Collection>): Promise<Collection> => {
    console.log('=== CREATE COLLECTION API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Collection data:', collectionData);
    
    try {
      const response = await medusaClient.admin.post('/collections', collectionData, {
        headers: getAuthHeaders(storeHandle, {}, true), // true = use medusa_token for admin APIs
      });
      
      console.log('Create collection API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error creating collection:', error);
      throw error;
    }
  },

  updateCollection: async (storeHandle: string, collectionId: string, collectionData: Partial<Collection>): Promise<Collection> => {
    console.log('=== UPDATE COLLECTION API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Collection ID:', collectionId);
    console.log('Collection data:', collectionData);
    
    try {
      const response = await medusaClient.admin.put(`/collections/${collectionId}`, collectionData, {
        headers: getAuthHeaders(storeHandle, {}, true), // true = use medusa_token for admin APIs
      });
      
      console.log('Update collection API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error updating collection:', error);
      throw error;
    }
  },

  deleteCollection: async (storeHandle: string, collectionId: string): Promise<void> => {
    console.log('=== DELETE COLLECTION API CALL ===');
    console.log('Store handle (x-tenant-id):', storeHandle);
    console.log('Collection ID:', collectionId);
    
    try {
      await medusaClient.admin.delete(`/collections/${collectionId}`, {
        headers: getAuthHeaders(storeHandle, {}, true), // true = use medusa_token for admin APIs
      });
      
      console.log('Collection deleted successfully');
    } catch (error: any) {
      console.error('Error deleting collection:', error);
      throw error;
    }
  },
};

export default collectionsApi;