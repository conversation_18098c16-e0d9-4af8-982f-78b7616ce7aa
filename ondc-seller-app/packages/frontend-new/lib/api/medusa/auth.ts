// lib/api/medusa/auth.ts
import axios from "@/lib/axios";
import signupAxios from "@/lib/signupAxios";

export const adminLogin = async (data: { username: string; password: string }) => { // username can be email or mobile number (91 + 10 digits)
  const response = await axios.post("/auth/onesso/login", data);
  return response.data;
};

export const getAdminUser = async (token: string) => {
  const response = await axios.get("/admin/users/me", {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
};

export const adminSignup = async (data: {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  store_name: string;
  store_handle: string;
}) => {
  const response = await signupAxios.post("/public/admin-signup", data);
  return response.data;
};
