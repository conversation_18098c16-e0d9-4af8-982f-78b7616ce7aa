// lib/api/services/medusa-admin-auth.ts
import { medusaAdminClient } from '../clients/medusa-admin-client';

export interface AdminLoginCredentials {
  email: string;
  password: string;
}

export interface AdminLoginResponse {
  access_token?: string;
  token?: string;
  accessToken?: string;
  authToken?: string;
  jwt?: string;
  user?: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
  };
  [key: string]: any;
}

export interface AdminSignupRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  store_name: string;
  store_handle: string;
}

export interface AdminSignupResponse {
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  store: {
    id: string;
    name: string;
    handle: string;
  };
  message?: string;
}

export interface AdminUserDetailsResponse {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  role?: string;
  created_at?: string;
  updated_at?: string;
  metadata?: {
    onboarding_status?: 'completed' | 'pending';
    store_handle?: string;
    profile_image?: string;
    contact_number?: string;
    bio?: string;
    [key: string]: any;
  };
  store_handle?: string;
  onboarding_status?: 'completed' | 'pending';
  [key: string]: any;
}

export interface AdminUserUpdateRequest {
  first_name: string;
  last_name: string;
  metadata?: {
    profile_image?: string;
    contact_number?: string;
    bio?: string;
    [key: string]: any;
  };
}

export interface AdminUserUpdateResponse {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  metadata?: {
    profile_image?: string;
    contact_number?: string;
    bio?: string;
    [key: string]: any;
  };
  updated_at: string;
  [key: string]: any;
}

// Medusa Admin Authentication Service
export const medusaAdminAuthService = {
  // Admin login
  adminLogin: async (credentials: AdminLoginCredentials): Promise<AdminLoginResponse> => {
    try {
      console.log('=== MEDUSA ADMIN LOGIN ===');
      console.log('Making admin login request to:', '/auth/onesso/login');
      console.log('Request payload:', { email: credentials.email });
      
      const response = await medusaAdminClient.post('/auth/onesso/login', credentials);
      
      console.log('=== MEDUSA ADMIN LOGIN RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data type:', typeof response.data);
      console.log('Response data:', response.data);
      
      if (!response.data) {
        console.error('No data in admin login response');
        throw new Error('No data received from admin login API');
      }
      
      return response.data;
    } catch (error: any) {
      console.error('=== MEDUSA ADMIN LOGIN ERROR ===');
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      }
      
      throw error;
    }
  },

  // Admin signup
  adminSignup: async (userData: AdminSignupRequest): Promise<AdminSignupResponse> => {
    try {
      console.log('=== MEDUSA ADMIN SIGNUP ===');
      console.log('Making admin signup request to:', '/public/admin-signup');
      console.log('Request payload:', { 
        email: userData.email, 
        store_name: userData.store_name,
        store_handle: userData.store_handle 
      });
      
      const response = await medusaAdminClient.post('/public/admin-signup', userData);
      
      console.log('=== MEDUSA ADMIN SIGNUP RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('=== MEDUSA ADMIN SIGNUP ERROR ===');
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      }
      
      throw error;
    }
  },

  // Get admin user details
  adminGetUserDetails: async (token?: string, storeHandle?: string): Promise<AdminUserDetailsResponse> => {
    try {
      console.log('=== MEDUSA ADMIN GET USER DETAILS ===');
      console.log('Token provided:', token ? 'yes' : 'no');
      console.log('Store handle:', storeHandle);
      
      const headers: any = {};
      if (storeHandle) {
        headers['x-tenant-id'] = storeHandle;
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await medusaAdminClient.get('/admin/users/me', { headers });
      
      console.log('=== MEDUSA ADMIN USER DETAILS RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      console.log('Response data keys:', Object.keys(response.data));
      
      // Check for store handle in different possible locations
      console.log('=== STORE HANDLE DETECTION IN ADMIN API RESPONSE ===');
      console.log('response.data.store_handle:', response.data.store_handle);
      console.log('response.data.metadata:', response.data.metadata);
      console.log('response.data.metadata?.store_handle:', response.data.metadata?.store_handle);
      
      return response.data;
    } catch (error: any) {
      console.error('=== MEDUSA ADMIN GET USER DETAILS ERROR ===');
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      if (error.response?.status === 401) {
        console.log('🚨 adminGetUserDetails returned 401 - token may be invalid');
        console.log('Current URL:', typeof window !== 'undefined' ? window.location.href : 'N/A');
      }
      
      throw error;
    }
  },

  // Update admin onboarding status
  adminUpdateOnboardingStatus: async (
    status: 'pending' | 'completed', 
    token?: string, 
    storeHandle?: string
  ): Promise<AdminUserDetailsResponse> => {
    try {
      console.log('=== MEDUSA ADMIN UPDATE ONBOARDING STATUS ===');
      console.log('Token provided:', token ? 'yes' : 'no');
      console.log('Store handle:', storeHandle);
      console.log('Status:', status);
      
      const headers: any = {};
      if (storeHandle) {
        headers['x-tenant-id'] = storeHandle;
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const payload = {
        metadata: {
          onboarding_status: status
        }
      };
      
      const response = await medusaAdminClient.patch('/admin/users/me', payload, { headers });
      
      console.log('=== MEDUSA ADMIN ONBOARDING STATUS UPDATE RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('=== MEDUSA ADMIN UPDATE ONBOARDING STATUS ERROR ===');
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      throw error;
    }
  },

  // Complete admin onboarding
  adminCompleteOnboarding: async (
    userId: string, 
    token?: string, 
    storeHandle?: string
  ): Promise<AdminUserDetailsResponse> => {
    try {
      console.log('=== MEDUSA ADMIN COMPLETE ONBOARDING ===');
      console.log('User ID:', userId);
      console.log('Token provided:', token ? 'yes' : 'no');
      console.log('Store handle:', storeHandle);
      
      const headers: any = {};
      if (storeHandle) {
        headers['x-tenant-id'] = storeHandle;
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const payload = {
        metadata: {
          onboarding_status: 'completed',
          onboarding_store_configuration: true,
          onboarding_add_product: true,
          onboarding_add_bulk_product: false
        }
      };
      
      console.log('Request payload:', payload);
      
      const response = await medusaAdminClient.patch(`/admin/users/${userId}`, payload, { headers });
      
      console.log('=== MEDUSA ADMIN COMPLETE ONBOARDING RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('=== MEDUSA ADMIN COMPLETE ONBOARDING ERROR ===');
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      throw error;
    }
  },

  // Update admin user profile
  adminUpdateUserProfile: async (
    userId: string, 
    userData: AdminUserUpdateRequest, 
    token?: string, 
    storeHandle?: string
  ): Promise<AdminUserUpdateResponse> => {
    try {
      console.log('=== MEDUSA ADMIN UPDATE USER PROFILE ===');
      console.log('User ID:', userId);
      console.log('Token provided:', token ? 'yes' : 'no');
      console.log('Store handle:', storeHandle);
      console.log('User data:', userData);
      
      const headers: any = {};
      if (storeHandle) {
        headers['x-tenant-id'] = storeHandle;
      }
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
      
      const response = await medusaAdminClient.post(`/admin/users/${userId}`, userData, { headers });
      
      console.log('=== MEDUSA ADMIN UPDATE USER PROFILE RESPONSE ===');
      console.log('Response status:', response.status);
      console.log('Response data:', response.data);
      
      return response.data;
    } catch (error: any) {
      console.error('=== MEDUSA ADMIN UPDATE USER PROFILE ERROR ===');
      console.error('Error message:', error.message);
      console.error('Error response:', error.response);
      
      if (error.response) {
        console.error('Error response status:', error.response.status);
        console.error('Error response data:', error.response.data);
      }
      
      throw error;
    }
  },
};