# Keycloak Register API + Medusa Signup Integration Plan

## Overview

This document outlines the pre-implementation plan for integrating Medusa's native signup API with the existing Keycloak register API. The goal is to create a unified registration flow that registers users in both Keycloak and Medusa systems simultaneously.

## Current State Analysis

### Existing Keycloak Register API
- **Endpoint**: `POST /auth/onesso/register`
- **Current Flow**: 
  1. Validates input (email, password, mobile, first_name, last_name)
  2. Calls `hybridAuthService.register()`
  3. Only registers in Keycloak via `keycloakAuthService.register()`
  4. Returns Keycloak registration response

### Existing Medusa Admin Signup API
- **Endpoint**: `POST /public/admin-signup`
- **Current Flow**:
  1. Validates input (email, password, firstName, lastName, phone, storeName, storeHandle)
  2. Creates user via CLI command: `npx medusa user --email --password`
  3. Updates user with complete data using Medusa user service
  4. Returns Medusa user data

### Current Issues
1. **Separate Systems**: Keycloak and Medusa registrations are completely separate
2. **No Synchronization**: Users registered in Keycloak don't automatically exist in Medusa
3. **Inconsistent Data**: Different field names and validation rules
4. **Manual Process**: Requires separate API calls for complete registration

## Integration Goals

### Primary Objectives
1. **Unified Registration**: Single API call registers user in both systems
2. **Data Consistency**: Ensure user data is synchronized between Keycloak and Medusa
3. **Fallback Mechanism**: Handle partial failures gracefully
4. **Backward Compatibility**: Maintain existing API contracts
5. **Transaction Safety**: Ensure data integrity across both systems

### Success Criteria
- ✅ Single API call creates user in both Keycloak and Medusa
- ✅ User data is consistent across both systems
- ✅ Proper error handling for partial failures
- ✅ Rollback mechanism for failed operations
- ✅ Comprehensive logging and monitoring
- ✅ Maintains existing API response format

## Implementation Strategy

### Approach 1: Enhanced Hybrid Registration (Recommended)

#### Overview
Enhance the existing `HybridAuthService.register()` method to include Medusa user creation as part of the registration flow.

#### Benefits
- ✅ Minimal API changes
- ✅ Leverages existing architecture
- ✅ Maintains backward compatibility
- ✅ Centralized registration logic

#### Implementation Steps

##### Phase 1: Service Layer Enhancement

1. **Enhance HybridAuthService.register()**
   ```javascript
   async register(userData) {
     const transaction = await this.startTransaction();
     try {
       // Step 1: Register in Keycloak
       const keycloakResult = await this.handleKeycloakRegistration(userData);
       
       // Step 2: Register in Medusa (NEW)
       const medusaResult = await this.handleMedusaRegistration(userData, keycloakResult);
       
       // Step 3: Sync user data
       const syncResult = await this.syncUserData(keycloakResult, medusaResult);
       
       await transaction.commit();
       return this.buildUnifiedResponse(keycloakResult, medusaResult, syncResult);
     } catch (error) {
       await transaction.rollback();
       throw error;
     }
   }
   ```

2. **Implement handleMedusaRegistration()**
   ```javascript
   async handleMedusaRegistration(userData, keycloakResult) {
     try {
       // Create Medusa user using the same approach as admin-signup
       const medusaUser = await this.createMedusaUser(userData);
       return {
         success: true,
         user: medusaUser,
         source: 'medusa'
       };
     } catch (error) {
       console.error('Medusa registration failed:', error);
       throw new MedusaRegistrationError(error.message);
     }
   }
   ```

3. **Implement createMedusaUser()**
   ```javascript
   async createMedusaUser(userData) {
     // Option A: Use CLI approach (current admin-signup method)
     const cliResult = await this.createUserViaCLI(userData);
     
     // Option B: Use direct service approach (preferred)
     const directResult = await this.createUserViaService(userData);
     
     return directResult || cliResult;
   }
   ```

##### Phase 2: Data Mapping and Validation

1. **Create Data Mapping Service**
   ```javascript
   class UserDataMapper {
     static mapToKeycloak(userData) {
       return {
         email: userData.email,
         password: userData.password,
         password_confirmation: userData.password,
         first_name: userData.first_name,
         last_name: userData.last_name,
         mobile: userData.mobile || userData.phone
       };
     }
     
     static mapToMedusa(userData, keycloakUser) {
       return {
         email: userData.email,
         first_name: userData.first_name,
         last_name: userData.last_name,
         metadata: {
           keycloak_user_id: keycloakUser?.user_id,
           user_type: userData.user_type || 'customer',
           phone: userData.mobile || userData.phone,
           registration_source: 'keycloak_hybrid'
         }
       };
     }
   }
   ```

2. **Enhanced Validation Schema**
   ```javascript
   const hybridRegisterSchema = z.object({
     // Common fields
     email: z.string().email('Invalid email format'),
     password: z.string().min(8, 'Password must be at least 8 characters'),
     first_name: z.string().min(1, 'First name is required'),
     last_name: z.string().min(1, 'Last name is required'),
     
     // Optional fields
     mobile: z.string().optional(),
     phone: z.string().optional(),
     user_type: z.enum(['customer', 'admin']).default('customer'),
     
     // Medusa-specific fields (optional)
     store_name: z.string().optional(),
     store_handle: z.string().optional(),
   }).refine(data => data.mobile || data.phone, {
     message: 'Either mobile or phone is required',
     path: ['mobile']
   });
   ```

##### Phase 3: Error Handling and Rollback

1. **Custom Error Classes**
   ```javascript
   class RegistrationError extends Error {
     constructor(message, source, originalError) {
       super(message);
       this.source = source;
       this.originalError = originalError;
     }
   }
   
   class KeycloakRegistrationError extends RegistrationError {}
   class MedusaRegistrationError extends RegistrationError {}
   class SyncError extends RegistrationError {}
   ```

2. **Rollback Mechanism**
   ```javascript
   async rollbackRegistration(keycloakResult, medusaResult) {
     const rollbackResults = [];
     
     // Rollback Medusa user if created
     if (medusaResult?.success && medusaResult?.user?.id) {
       try {
         await this.deleteMedusaUser(medusaResult.user.id);
         rollbackResults.push({ service: 'medusa', status: 'rolled_back' });
       } catch (error) {
         rollbackResults.push({ service: 'medusa', status: 'rollback_failed', error });
       }
     }
     
     // Note: Keycloak rollback might not be possible depending on API
     // Log for manual cleanup if needed
     if (keycloakResult?.success) {
       rollbackResults.push({ 
         service: 'keycloak', 
         status: 'manual_cleanup_required',
         user_id: keycloakResult.user?.user_id 
       });
     }
     
     return rollbackResults;
   }
   ```

##### Phase 4: Response Unification

1. **Unified Response Builder**
   ```javascript
   buildUnifiedResponse(keycloakResult, medusaResult, syncResult) {
     return {
       success: true,
       message: 'Registration successful in both systems',
       data: {
         user: {
           id: medusaResult?.user?.id,
           keycloak_id: keycloakResult?.user?.user_id,
           email: keycloakResult?.user?.email,
           first_name: keycloakResult?.user?.first_name,
           last_name: keycloakResult?.user?.last_name,
           mobile: keycloakResult?.user?.mobile
         },
         registration_details: {
           keycloak: {
             success: keycloakResult?.success || false,
             user_id: keycloakResult?.user?.user_id
           },
           medusa: {
             success: medusaResult?.success || false,
             user_id: medusaResult?.user?.id
           },
           sync: {
             success: syncResult?.success || false,
             action: syncResult?.action
           }
         },
         tokens: keycloakResult?.tokens || null
       }
     };
   }
   ```

### Approach 2: New Unified Endpoint (Alternative)

#### Overview
Create a new endpoint that handles both Keycloak and Medusa registration.

#### Benefits
- ✅ Clean separation of concerns
- ✅ Easier to test and maintain
- ✅ More explicit about dual registration

#### Drawbacks
- ❌ Requires new API endpoint
- ❌ Potential code duplication
- ❌ More complex for clients

## Technical Implementation Details

### 1. Service Integration Points

#### A. Medusa User Service Integration
```javascript
// In HybridAuthService
async createMedusaUser(userData) {
  if (!this.container) {
    throw new Error('Medusa container not available');
  }
  
  try {
    const userService = this.container.resolve('userModuleService');
    
    // Create user directly via service
    const medusaUser = await userService.createUsers([{
      email: userData.email,
      first_name: userData.first_name,
      last_name: userData.last_name,
      metadata: {
        keycloak_synced: true,
        registration_source: 'hybrid',
        user_type: userData.user_type || 'customer'
      }
    }]);
    
    return medusaUser[0];
  } catch (error) {
    // Fallback to CLI method
    return await this.createUserViaCLI(userData);
  }
}
```

#### B. Authentication Integration
```javascript
// Enhanced login to check both systems
async login(credentials) {
  const keycloakResult = await this.handleKeycloakLogin(credentials);
  
  if (keycloakResult?.success) {
    // Ensure user exists in Medusa
    await this.ensureMedusaUserExists(keycloakResult.user);
  }
  
  return keycloakResult;
}
```

### 2. Database Schema Considerations

#### User Metadata Enhancement
```javascript
// Enhanced user metadata structure
const userMetadata = {
  // Existing fields
  keycloak_user_id: 'string',
  keycloak_username: 'string',
  tenant_id: 'string',
  
  // New fields for hybrid registration
  registration_source: 'hybrid|keycloak|medusa',
  keycloak_synced: true,
  medusa_synced: true,
  sync_timestamp: '2024-12-01T10:00:00Z',
  registration_method: 'api|cli|admin',
  
  // User type and role information
  user_type: 'customer|admin',
  roles: ['customer', 'admin'],
  
  // Additional profile data
  phone: 'string',
  store_name: 'string',
  store_handle: 'string'
};
```

### 3. Configuration Updates

#### Environment Variables
```bash
# Add to .env
HYBRID_REGISTRATION_ENABLED=true
MEDUSA_REGISTRATION_METHOD=service  # or 'cli'
REGISTRATION_ROLLBACK_ENABLED=true
REGISTRATION_TIMEOUT=30000

# Medusa Service Configuration
MEDUSA_USER_SERVICE_NAME=userModuleService
MEDUSA_CUSTOMER_SERVICE_NAME=customerService

# Registration Flow Configuration
KEYCLOAK_FIRST_REGISTRATION=true  # Register in Keycloak first
MEDUSA_SYNC_IMMEDIATE=true        # Sync to Medusa immediately
REGISTRATION_TRANSACTION_ENABLED=true
```

#### Auth Configuration Updates
```javascript
// In auth-config.js
class AuthConfigService {
  isHybridRegistrationEnabled() {
    return this.parseBoolean(process.env.HYBRID_REGISTRATION_ENABLED, true);
  }
  
  getMedusaRegistrationMethod() {
    return process.env.MEDUSA_REGISTRATION_METHOD || 'service';
  }
  
  isRegistrationRollbackEnabled() {
    return this.parseBoolean(process.env.REGISTRATION_ROLLBACK_ENABLED, true);
  }
}
```

## API Contract Changes

### Enhanced Request Schema
```javascript
// POST /auth/onesso/register
{
  // Required fields
  "email": "<EMAIL>",
  "password": "securePassword123",
  "first_name": "John",
  "last_name": "Doe",
  
  // Optional fields
  "mobile": "+1234567890",
  "phone": "+1234567890",  // Alternative to mobile
  "user_type": "customer", // or "admin"
  
  // Medusa-specific fields (optional)
  "store_name": "My Store",
  "store_handle": "my-store",
  
  // System fields
  "registration_source": "api" // auto-set
}
```

### Enhanced Response Schema
```javascript
// Success Response
{
  "success": true,
  "message": "Registration successful in both systems",
  "data": {
    "user": {
      "id": "medusa_user_id",
      "keycloak_id": "keycloak_user_id",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "mobile": "+1234567890"
    },
    "registration_details": {
      "keycloak": {
        "success": true,
        "user_id": "keycloak_user_id"
      },
      "medusa": {
        "success": true,
        "user_id": "medusa_user_id"
      },
      "sync": {
        "success": true,
        "action": "created"
      }
    },
    "tokens": {
      "access_token": "jwt_token",
      "refresh_token": "refresh_token",
      "expires_in": 3600
    }
  }
}
```

### Error Response Schema
```javascript
// Partial Failure Response
{
  "success": false,
  "message": "Partial registration failure",
  "error": {
    "code": "PARTIAL_REGISTRATION_FAILURE",
    "details": {
      "keycloak": {
        "success": true,
        "user_id": "keycloak_user_id"
      },
      "medusa": {
        "success": false,
        "error": "User service not available"
      },
      "rollback": {
        "attempted": true,
        "results": [
          {
            "service": "keycloak",
            "status": "manual_cleanup_required"
          }
        ]
      }
    }
  }
}
```

## Implementation Phases

### Phase 1: Foundation (Week 1)
- [ ] Create data mapping utilities
- [ ] Enhance validation schemas
- [ ] Implement error handling classes
- [ ] Add configuration options
- [ ] Create unit tests for utilities

### Phase 2: Core Integration (Week 2)
- [ ] Implement `handleMedusaRegistration()` method
- [ ] Enhance `HybridAuthService.register()` method
- [ ] Implement rollback mechanism
- [ ] Add transaction support
- [ ] Create integration tests

### Phase 3: API Enhancement (Week 3)
- [ ] Update registration endpoint
- [ ] Implement unified response builder
- [ ] Add comprehensive error handling
- [ ] Update API documentation
- [ ] Create end-to-end tests

### Phase 4: Testing & Optimization (Week 4)
- [ ] Performance testing
- [ ] Error scenario testing
- [ ] Load testing
- [ ] Security testing
- [ ] Documentation updates

## Risk Assessment

### High Risk
1. **Data Inconsistency**: Partial failures could leave systems out of sync
   - **Mitigation**: Implement robust rollback mechanism
   
2. **Performance Impact**: Dual registration increases response time
   - **Mitigation**: Implement async processing for non-critical operations

3. **Service Dependencies**: Failure in one system affects the other
   - **Mitigation**: Implement circuit breaker pattern

### Medium Risk
1. **Backward Compatibility**: Changes might break existing clients
   - **Mitigation**: Maintain existing API contracts, add feature flags

2. **Complex Error Handling**: Multiple failure points increase complexity
   - **Mitigation**: Comprehensive error mapping and logging

### Low Risk
1. **Configuration Complexity**: More configuration options to manage
   - **Mitigation**: Provide sensible defaults and clear documentation

## Testing Strategy

### Unit Tests
- [ ] Data mapping functions
- [ ] Validation schemas
- [ ] Error handling classes
- [ ] Service methods

### Integration Tests
- [ ] Keycloak + Medusa registration flow
- [ ] Rollback mechanisms
- [ ] Error scenarios
- [ ] User synchronization

### End-to-End Tests
- [ ] Complete registration flow
- [ ] Login after registration
- [ ] User data consistency
- [ ] Performance benchmarks

## Monitoring and Observability

### Metrics to Track
- Registration success rate (overall)
- Keycloak registration success rate
- Medusa registration success rate
- Rollback frequency
- Response time percentiles
- Error rates by type

### Logging Strategy
```javascript
// Structured logging example
logger.info('Hybrid registration started', {
  email: userData.email,
  user_type: userData.user_type,
  tenant_id: tenantId,
  registration_id: generateId()
});

logger.info('Keycloak registration completed', {
  registration_id: registrationId,
  success: true,
  keycloak_user_id: result.user_id,
  duration_ms: 1250
});

logger.error('Medusa registration failed', {
  registration_id: registrationId,
  error: error.message,
  keycloak_user_id: keycloakResult.user_id,
  rollback_required: true
});
```

## Security Considerations

### Data Protection
- [ ] Ensure passwords are not logged
- [ ] Implement proper data sanitization
- [ ] Add rate limiting for registration endpoint
- [ ] Validate all input data thoroughly

### Access Control
- [ ] Maintain existing authentication requirements
- [ ] Implement proper tenant isolation
- [ ] Add audit logging for registration events

### Error Information
- [ ] Avoid exposing internal system details in errors
- [ ] Implement proper error sanitization
- [ ] Log detailed errors internally only

## Rollback Strategy

### Automatic Rollback
1. **Medusa User Deletion**: If Medusa user was created but sync failed
2. **Cleanup Metadata**: Remove any partial data created
3. **Log Rollback Actions**: Track all rollback operations

### Manual Rollback
1. **Keycloak User Cleanup**: May require manual intervention
2. **Data Consistency Checks**: Periodic validation of user data
3. **Monitoring Alerts**: Alert on rollback failures

## Success Metrics

### Technical Metrics
- [ ] 99%+ registration success rate
- [ ] <2 second average response time
- [ ] <1% rollback rate
- [ ] Zero data inconsistencies

### Business Metrics
- [ ] Improved user onboarding experience
- [ ] Reduced support tickets for registration issues
- [ ] Increased user activation rate

## Conclusion

This integration plan provides a comprehensive approach to unifying Keycloak and Medusa registration while maintaining system reliability and data consistency. The phased implementation approach allows for iterative development and testing, reducing risk and ensuring a smooth rollout.

The recommended approach (Enhanced Hybrid Registration) leverages the existing architecture while adding the necessary functionality to create users in both systems. This maintains backward compatibility while providing the unified registration experience required.

Key success factors:
1. **Robust error handling and rollback mechanisms**
2. **Comprehensive testing at all levels**
3. **Proper monitoring and observability**
4. **Gradual rollout with feature flags**
5. **Clear documentation and team training**

---

**Next Steps**: Review this plan with the development team, prioritize implementation phases, and begin with Phase 1 foundation work.