import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

type Body = {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  storeName?: string;
  storeHandle?: string;
  userType?: string;
};

// CORS headers for admin signup - dynamically set based on request origin
const getAllowedOrigins = () => {
  const adminCors = process.env.ADMIN_CORS || 'http://localhost:3000,http://localhost:3001';
  return adminCors.split(',').map(origin => origin.trim());
};

const getCorsHeaders = (requestOrigin?: string) => {
  const allowedOrigins = getAllowedOrigins();
  const origin =
    requestOrigin && allowedOrigins.includes(requestOrigin) ? requestOrigin : allowedOrigins[0]; // Default to first allowed origin

  return {
    'Access-Control-Allow-Origin': origin,
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Tenant-ID',
    'Access-Control-Allow-Credentials': 'true',
    'Access-Control-Max-Age': '86400',
  };
};

// Handle preflight OPTIONS requests
export const OPTIONS = async (req: MedusaRequest, res: MedusaResponse) => {
  const origin = req.headers.origin;
  const corsHeaders = getCorsHeaders(origin);

  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });
  return res.status(200).end();
};

export const POST = async (req: MedusaRequest<Body>, res: MedusaResponse) => {
  // Set CORS headers for all responses
  const origin = req.headers.origin;
  const corsHeaders = getCorsHeaders(origin);

  Object.entries(corsHeaders).forEach(([key, value]) => {
    res.setHeader(key, value);
  });

  try {
    console.log('🔐 Admin signup request:', { email: req.body?.email });
    const { email, password, firstName, lastName, phone, storeName, storeHandle, userType } =
      req.body;

    // Get tenant ID from header
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';
    console.log('🏢 Tenant ID:', tenantId);

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        message: 'Email and password are required',
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        message: 'Invalid email format',
      });
    }

    // Validate password strength
    if (password.length < 8) {
      return res.status(400).json({
        message: 'Password must be at least 8 characters long',
      });
    }

    console.log('🎯 Using two-step process: CLI user creation + user data update...');

    // STEP 1: Create basic user using CLI command
    console.log('📝 STEP 1: Creating basic user via CLI command...');

    try {
      // Execute the CLI command from the correct directory
      const workingDirectory = process.cwd();
      const cliCommand = `npx medusa user --email "${email}" --password "${password}"`;

      console.log('🔧 Working directory:', workingDirectory);
      console.log('🔧 Executing CLI command:', cliCommand);

      const { stdout, stderr } = await execAsync(cliCommand, {
        cwd: workingDirectory,
        timeout: 30000, // 30 second timeout
      });

      console.log('🔍 CLI stdout:', stdout);
      console.log('🔍 CLI stderr:', stderr);

      // Check for user already exists error
      if (
        stdout.includes('Identity with email already exists') ||
        stderr.includes('Identity with email already exists')
      ) {
        console.log('⚠️ User already exists, returning appropriate error');
        return res.status(400).json({
          message: 'User with this email already exists',
        });
      }

      // Check if the command was successful
      if (stderr && stderr.includes('Error')) {
        console.error('❌ CLI command failed with error:', stderr);
        return res.status(400).json({
          message: `Failed to create user account: ${stderr}`,
        });
      }

      // Check for various success indicators in the output
      const successIndicators = [
        'User created successfully',
        'created successfully',
        'User with email',
        'successfully created',
      ];

      const hasSuccess = successIndicators.some(
        indicator =>
          stdout.toLowerCase().includes(indicator.toLowerCase()) ||
          stderr.toLowerCase().includes(indicator.toLowerCase())
      );

      if (!hasSuccess) {
        console.error('❌ CLI command did not report success');
        console.log('🔍 Full stdout:', stdout);
        console.log('🔍 Full stderr:', stderr);
        return res.status(400).json({
          message: 'CLI command did not complete successfully',
        });
      }

      console.log('✅ STEP 1 COMPLETED: User created successfully via CLI command');
    } catch (cliError) {
      console.error('❌ CLI command execution failed:', cliError);
      console.log('🔍 CLI error details:', {
        message: cliError.message,
        code: cliError.code,
        signal: cliError.signal,
        stdout: cliError.stdout,
        stderr: cliError.stderr,
      });
      return res.status(400).json({
        message: `Failed to create user account via CLI: ${cliError.message}`,
      });
    }

    // STEP 2: Update user with complete data
    console.log('🔄 STEP 2: Updating user with complete data...');

    try {
      // Get user service from container - try different service names
      let userService: any = null;
      const serviceNames = ['user', 'userModuleService', 'userModule'];

      for (const serviceName of serviceNames) {
        try {
          userService = req.scope.resolve(serviceName);
          console.log(`✅ User service resolved successfully as: ${serviceName}`);
          break;
        } catch (error) {
          console.log(`❌ Failed to resolve service: ${serviceName}`);
        }
      }

      if (!userService) {
        console.error('❌ Could not resolve user service with any known name');
        return res.status(500).json({
          message: 'User service not available for data update',
        });
      }

      // Find the newly created user by email
      console.log(`🔍 Searching for user with email: ${email}`);
      let users: any[] = [];

      try {
        if (typeof userService.listUsers === 'function') {
          users = await userService.listUsers({ email: email });
        } else if (typeof userService.list === 'function') {
          users = await userService.list({ email: email });
        } else if (typeof userService.retrieveByEmail === 'function') {
          const user = await userService.retrieveByEmail(email);
          users = user ? [user] : [];
        } else {
          console.log('❌ No suitable method found to fetch users');
          console.log(
            'Available methods:',
            Object.getOwnPropertyNames(Object.getPrototypeOf(userService))
          );
          return res.status(500).json({
            message: 'Cannot find user after CLI creation - service method not available',
          });
        }
      } catch (fetchError) {
        console.error('❌ Error fetching user:', fetchError);
        return res.status(500).json({
          message: 'User was created but could not be found for updating',
        });
      }

      if (!users || users.length === 0) {
        console.error('❌ User not found after CLI creation');
        return res.status(500).json({
          message: 'User was created but could not be found for updating',
        });
      }

      const createdUser = users[0];
      console.log('✅ User found:', { id: createdUser.id, email: createdUser.email });

      // STEP 2: Update user with complete data
      console.log('� Setting up authentication for user...');

      // Prepare complete user data for update
      const userData = {
        email: email,
        first_name: firstName || 'Admin',
        last_name: lastName || 'User',
        avatar_url: null,
        metadata: {
          user_type: userType,
          tenant_id: tenantId,
          phone: phone,
          store_name: storeName,
          store_handle: storeHandle,
          onboarding_status: 'pending',
          onboarding_add_product: false,
          onboarding_add_bulk_product: false,
          onboarding_store_configuration: false,
        },
      };

      console.log('🔄 Updating user with complete data:', {
        email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        metadata_keys: Object.keys(userData.metadata),
      });

      let updatedUser: any = null;

      try {
        // Try different methods to update user with correct parameters
        if (typeof userService.updateUsers === 'function') {
          // updateUsers expects an array of updates with id and data
          const updateResult = await userService.updateUsers([
            {
              id: createdUser.id,
              ...userData,
            },
          ]);
          updatedUser = Array.isArray(updateResult) ? updateResult[0] : updateResult;
        } else if (typeof userService.update === 'function') {
          updatedUser = await userService.update(createdUser.id, userData);
        } else if (typeof userService.updateUser === 'function') {
          updatedUser = await userService.updateUser(createdUser.id, userData);
        } else {
          console.log('❌ No suitable method found to update users');
          console.log(
            'Available methods:',
            Object.getOwnPropertyNames(Object.getPrototypeOf(userService))
          );
          return res.status(500).json({
            message: 'User created but update method not available',
          });
        }

        if (!updatedUser) {
          console.log('⚠️ User update returned null or undefined');
          return res.status(500).json({
            message: 'User created but update operation failed',
          });
        }

        console.log('✅ STEP 2 COMPLETED: User updated successfully with complete data');
      } catch (updateError) {
        console.error('❌ Error updating user with complete data:', updateError);
        return res.status(500).json({
          message: `User created but failed to update with complete data: ${updateError.message}`,
        });
      }

      // Both steps completed successfully
      console.log('✅ Admin signup completed successfully - both steps completed');

      return res.status(201).json({
        message: 'Admin account created successfully',
        user: {
          id: updatedUser.id || createdUser.id,
          email: updatedUser.email || createdUser.email,
          first_name: updatedUser.first_name || userData.first_name,
          last_name: updatedUser.last_name || userData.last_name,
          metadata: updatedUser.metadata || userData.metadata,
        },
        success: true,
        autoLoginSuccess: false, // Frontend expects this field
        isNewUser: true, // Frontend expects this field
        steps_completed: {
          cli_user_creation: true,
          user_data_update: true,
        },
      });
    } catch (userCreationError) {
      console.error('❌ Error creating user:', userCreationError);
      return res.status(400).json({
        message: `Failed to create user account: ${userCreationError.message}`,
      });
    }
  } catch (error) {
    console.error('❌ Admin signup error:', error);
    return res.status(500).json({
      message: 'Internal server error during signup',
    });
  }
};
