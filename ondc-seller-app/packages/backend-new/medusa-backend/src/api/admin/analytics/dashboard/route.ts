import type { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { z } from 'zod';
import { TenantServiceFactory } from '../../../../services/tenant-service-factory';
import { centralizedDb } from '../../../../services/centralized-database';

// Query parameter validation schema with enhanced period support
const DashboardQuerySchema = z.object({
  period: z
    .enum(['yearly', 'quarterly', 'monthly', 'weekly', '7d', '30d', '90d', '1y'])
    .default('monthly'),
  sales_channel_id: z.string().optional(),
  currency: z.string().default('INR'),
});

// Response interfaces
interface DashboardOverview {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  totalProducts: number;
  averageOrderValue: number;
  conversionRate: number;
  revenueGrowth: number;
  orderGrowth: number;
  customerGrowth: number;
}

interface SalesTrend {
  date: string;
  revenue: number;
  orders: number;
  customers: number;
}

interface TopProduct {
  productId: string;
  title: string;
  handle?: string;
  sku: string;
  revenue: number;
  units: number;
  stock: number;
  gross: number;
  price?: number;
  sale_price?: number;
  inventory_status?: string;
}

interface RecentOrder {
  order_id: string;
  order_display_id: string;
  customer_name: string;
  customer_email: string;
  total_order_amount: number;
  order_status: string;
  created_at: string;
}

interface DashboardAnalytics {
  stats: DashboardOverview;
  revenueTrend: SalesTrend[];
  topProducts: TopProduct[];
  topOrders: RecentOrder[];
  refundRate: Array<{ name: string; value: number; color: string }>;
  customerSplit: Array<{ segment: string; count: number; percentage: number }>;
}

/**
 * GET /admin/analytics/dashboard
 *
 * Multi-tenant dashboard analytics endpoint that provides:
 * - Overview statistics (revenue, orders, customers, products) filtered by tenant
 * - Sales trends over time with tenant isolation
 * - Top performing products for the tenant
 * - Recent orders for the tenant
 * - Revenue chart data in the exact format: [{date: "YYYY-MM-DD", revenue: number, orders: number}, ...]
 * - Refund rate analysis
 * - Customer segmentation
 *
 * Authentication & Tenant Isolation:
 * - Extracts tenant ID from x-tenant-id header (handled by tenant middleware)
 * - All database queries are filtered by tenant ID for security
 * - Returns 400 if tenant ID is missing or invalid
 */
export async function GET(req: MedusaRequest, res: MedusaResponse): Promise<any> {
  try {
    console.log('🔍 Dashboard API called with query:', req.query);
    console.log('🔍 Headers:', req.headers);

    // Extract tenant ID from header (consistent with other endpoints)
    const tenantId = (req.headers['x-tenant-id'] as string) || 'default';

    if (!tenantId || tenantId === 'default') {
      console.error('❌ No tenant ID found in request headers');
      return res.status(400).json({
        error: 'TENANT_REQUIRED',
        message: 'Tenant ID is required. Please provide x-tenant-id header.',
      });
    }

    // Validate query parameters
    const query = DashboardQuerySchema.parse(req.query);
    const { period, sales_channel_id, currency } = query;

    console.log('✅ Query validation passed:', { ...query, tenantId });

    // Get services using proper Medusa v2 service resolution
    const orderService = req.scope.resolve('order');
    const productService = req.scope.resolve('product');
    const customerService = req.scope.resolve('customer');

    // Calculate date range based on period with enhanced support
    const endDate = new Date();
    const startDate = new Date();

    switch (period) {
      case 'weekly':
        startDate.setDate(endDate.getDate() - 7); // Last 7 days for weekly view
        break;
      case 'monthly':
        startDate.setMonth(endDate.getMonth() - 12); // Last 12 months
        break;
      case 'quarterly':
        startDate.setMonth(endDate.getMonth() - 12); // Last 4 quarters (12 months)
        break;
      case 'yearly':
        startDate.setFullYear(endDate.getFullYear() - 5); // Last 5 years
        break;
      // Legacy support for existing periods
      case '7d':
        startDate.setDate(endDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(endDate.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(endDate.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(endDate.getFullYear() - 1); // Last 1 year
        break;
    }

    // Use tenant-aware services to fetch orders
    console.log('🔍 [DASHBOARD] === USING TENANT-AWARE SERVICES FOR ANALYTICS ===');

    // Create tenant-aware services using the factory
    const services = TenantServiceFactory.fromRequest(req);
    console.log(`🔗 [DASHBOARD] Using TenantServiceFactory for tenant: ${tenantId}`);

    let allOrders: any[] = [];

    try {
      // Use centralized database to fetch orders with tenant filtering
      console.log(`🔗 [DASHBOARD] Using centralized database for tenant: ${tenantId}`);

      const ordersQuery = `
        SELECT DISTINCT ON (o.id)
          o.id,
          o.status,
          o.created_at,
          o.updated_at,
          o.customer_id,
          o.email,
          o.currency_code,
          o.tenant_id,
          o.display_id,
          -- Get totals from order_summary table with correct join
          os.totals,
          -- Customer details for better analytics
          c.first_name as customer_first_name,
          c.last_name as customer_last_name,
          c.email as customer_email
        FROM "order" o
        LEFT JOIN order_summary os ON o.id = os.order_id
        LEFT JOIN customer c ON o.customer_id = c.id
        WHERE o.tenant_id = $1 AND o.deleted_at IS NULL
        ORDER BY o.id, o.created_at DESC
      `;

      const ordersResult = await centralizedDb.query(ordersQuery, [tenantId], { tenantId });
      allOrders = ordersResult.rows || [];

      console.log(`🔍 Total orders fetched from DB: ${allOrders.length}`);

      // Fetch order items for all orders to get complete order data
      if (allOrders.length > 0) {
        const orderIds = allOrders.map(order => order.id);
        const itemsQuery = `
          SELECT
            oli.id as line_item_id,
            oli.title,
            oli.unit_price,
            oli.variant_id,
            oli.product_id,
            oli.product_title,
            oli.variant_title,
            oli.variant_sku,
            -- Order item quantities
            oi.order_id,
            oi.quantity,
            oi.fulfilled_quantity,
            -- Product information
            p.id as product_id,
            p.title as product_title,
            p.handle as product_handle,
            -- Variant information
            pv.id as variant_id,
            pv.title as variant_title,
            pv.sku as variant_sku
          FROM order_line_item oli
          LEFT JOIN order_item oi ON oli.id = oi.item_id AND oi.deleted_at IS NULL
          LEFT JOIN product p ON oli.product_id = p.id AND p.deleted_at IS NULL
          LEFT JOIN product_variant pv ON oli.variant_id = pv.id AND pv.deleted_at IS NULL
          WHERE oi.order_id = ANY($1)
          AND oli.deleted_at IS NULL
          ORDER BY oli.created_at ASC
        `;

        const itemsResult = await centralizedDb.query(itemsQuery, [orderIds], { tenantId });
        const orderItems = itemsResult.rows || [];

        // Attach items to orders
        allOrders = allOrders.map(order => {
          const items = orderItems.filter(item => item.order_id === order.id);
          return {
            ...order,
            items: items.map(item => ({
              id: item.line_item_id,
              title: item.title,
              quantity: item.quantity || 0,
              unit_price: item.unit_price || 0,
              total: (item.unit_price || 0) * (item.quantity || 0),
              variant_id: item.variant_id,
              product_id: item.product_id,
              product: item.product_id
                ? {
                    id: item.product_id,
                    title: item.product_title,
                    handle: item.product_handle,
                  }
                : null,
              variant: item.variant_id
                ? {
                    id: item.variant_id,
                    title: item.variant_title,
                    sku: item.variant_sku,
                  }
                : null,
            })),
          };
        });

        console.log(`📦 Fetched items for ${allOrders.length} orders`);
      }

      allOrders.forEach((order: any, index: number) => {
        console.log(`🔍 Order ${index + 1}:`, {
          id: order.id,
          tenant_id: order.tenant_id,
          status: order.status,
          totals: order.totals,
          items_count: order.items?.length || 0,
          created_at: order.created_at,
        });
      });

      console.log(`📊 Found ${allOrders.length} orders with items for tenant: ${tenantId}`);
    } catch (orderError) {
      console.error('❌ Error fetching orders:', orderError);
      // Fallback to empty array if orders can't be fetched
      allOrders = [];
    }

    // Filter orders by date range and status (client-side filtering)
    console.log(`🔍 Date range filter: ${startDate.toISOString()} to ${endDate.toISOString()}`);

    // Apply proper date filtering for revenue trend calculations
    const orders = allOrders.filter((order: any) => {
      const orderDate = new Date(order.created_at);
      const isInDateRange = orderDate >= startDate && orderDate <= endDate;

      // Debug each order's date filtering
      console.log(`🔍 Order ${order.id}:`, {
        created_at: order.created_at,
        orderDate: orderDate.toISOString(),
        isInDateRange,
        status: order.status,
      });

      // Use proper date filtering for accurate revenue trends
      return isInDateRange;
    });

    console.log(
      `📊 Orders after date filtering: ${orders.length} (from ${allOrders.length} total)`
    );

    // Fetch products data with complete details using centralized database
    let products: any[] = [];
    try {
      const productsQuery = `
        SELECT
          p.id,
          p.title,
          p.handle,
          p.description,
          p.status,
          p.created_at,
          p.updated_at,
          p.tenant_id,
          p.thumbnail,
          -- Get variant details including pricing and quantity
          COALESCE(
            json_agg(
              DISTINCT jsonb_build_object(
                'id', pv.id,
                'title', pv.title,
                'sku', pv.sku,
                'metadata', pv.metadata
              )
            ) FILTER (WHERE pv.id IS NOT NULL), '[]'
          ) as variants
        FROM product p
        LEFT JOIN product_variant pv ON p.id = pv.product_id AND pv.tenant_id = p.tenant_id AND pv.deleted_at IS NULL
        WHERE p.tenant_id = $1 AND p.deleted_at IS NULL
        GROUP BY p.id, p.title, p.handle, p.description, p.status, p.created_at, p.updated_at, p.tenant_id, p.thumbnail
        ORDER BY p.created_at DESC
      `;

      const productsResult = await centralizedDb.query(productsQuery, [tenantId], { tenantId });
      products = (productsResult.rows || []).map((product: any) => {
        // Parse variants and extract pricing/quantity information
        const variants = Array.isArray(product.variants) ? product.variants : [];
        const firstVariant = variants[0];

        // Extract metadata information
        let variantMetadata = {};
        if (firstVariant?.metadata) {
          try {
            variantMetadata =
              typeof firstVariant.metadata === 'string'
                ? JSON.parse(firstVariant.metadata)
                : firstVariant.metadata;
          } catch (e) {
            console.warn(`Failed to parse variant metadata for product ${product.id}`);
          }
        }

        return {
          ...product,
          variants,
          // Add extracted details for analytics
          product_handle: product.handle,
          product_quantity: variantMetadata.product_quantity || 0,
          original_price: variantMetadata.original_price || 0,
          sale_price: variantMetadata.sale_price || variantMetadata.original_price || 0,
          product_inventory_status: variantMetadata.product_inventory_status || 'in_stock',
        };
      });

      console.log(
        `📦 Fetched ${products.length} products with complete details for tenant: ${tenantId}`
      );
    } catch (productError) {
      console.error('❌ Error fetching products:', productError);
      products = [];
    }

    // Fetch customers data using centralized database
    let customers: any[] = [];
    try {
      const customersQuery = `
        SELECT
          c.id,
          c.email,
          c.first_name,
          c.last_name,
          c.phone,
          c.created_at,
          c.updated_at,
          c.tenant_id
        FROM customer c
        WHERE c.tenant_id = $1 AND c.deleted_at IS NULL
        ORDER BY c.created_at DESC
        LIMIT 1000
      `;

      const customersResult = await centralizedDb.query(customersQuery, [tenantId], { tenantId });
      customers = customersResult.rows || [];

      console.log(`👥 Fetched ${customers.length} customers for tenant: ${tenantId}`);
    } catch (customerError) {
      console.error('❌ Error fetching customers:', customerError);
      customers = [];
    }

    console.log('📊 Data fetched:', {
      tenantId,
      totalOrders: allOrders.length,
      filteredOrders: orders.length,
      totalProducts: products.length,
      totalCustomers: customers.length,
    });

    // Calculate overview statistics
    // Handle totals from order_summary table (JSON format) - same as orders endpoint
    const totalRevenue = orders.reduce((sum: number, order: any) => {
      let orderTotal = 0;

      // Parse totals JSON (same logic as orders endpoint)
      let totals: any = {};
      if (order.totals) {
        try {
          totals = typeof order.totals === 'string' ? JSON.parse(order.totals) : order.totals;
        } catch (e) {
          console.warn(`Failed to parse totals JSON for order ${order.id}:`, e);
          totals = {};
        }
      }

      // Use current_order_total (same as orders endpoint)
      orderTotal = totals.current_order_total || 0;

      console.log(
        `💰 Order ${order.id}: totals = ${JSON.stringify(order.totals)}, current_order_total = ${orderTotal}`
      );
      return sum + orderTotal;
    }, 0);
    const totalOrders = orders.length;
    const totalCustomers = customers.length;
    const totalProducts = products.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    // Calculate growth rates (comparing with previous period)
    const previousStartDate = new Date(startDate);
    const previousEndDate = new Date(startDate);
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    previousStartDate.setDate(previousStartDate.getDate() - periodDays);

    // Filter previous period orders (client-side filtering)
    const previousOrders = allOrders.filter(order => {
      const orderDate = new Date(order.created_at);
      return orderDate >= previousStartDate && orderDate <= previousEndDate;
    });

    const previousRevenue = previousOrders.reduce((sum: number, order: any) => {
      let orderTotal = 0;

      // Use same parsing logic as current orders
      let totals: any = {};
      if (order.totals) {
        try {
          totals = typeof order.totals === 'string' ? JSON.parse(order.totals) : order.totals;
        } catch (e) {
          console.warn(`Failed to parse totals JSON for previous order ${order.id}:`, e);
          totals = {};
        }
      }

      orderTotal = totals.current_order_total || 0;
      return sum + orderTotal;
    }, 0);
    const revenueGrowth =
      previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const orderGrowth =
      previousOrders.length > 0
        ? ((totalOrders - previousOrders.length) / previousOrders.length) * 100
        : 0;

    // Generate revenue chart data based on selected period
    const revenueTrend: SalesTrend[] = [];

    // Helper function to calculate revenue for a given date range
    const calculateRevenueForRange = (rangeStart: Date, rangeEnd: Date) => {
      // Use allOrders instead of filtered orders to get accurate date range data
      const rangeOrders = allOrders.filter((order: any) => {
        const orderDate = new Date(order.created_at);
        return orderDate >= rangeStart && orderDate <= rangeEnd;
      });

      const rangeRevenue = rangeOrders.reduce((sum: number, order: any) => {
        let orderTotal = 0;

        // Use consistent parsing logic
        let totals: any = {};
        if (order.totals) {
          try {
            totals = typeof order.totals === 'string' ? JSON.parse(order.totals) : order.totals;
          } catch (e) {
            console.warn(`Failed to parse totals JSON for range order ${order.id}:`, e);
            totals = {};
          }
        }

        orderTotal = totals.current_order_total || 0;
        return sum + orderTotal;
      }, 0);

      console.log(
        `📊 Range ${rangeStart.toISOString().split('T')[0]} to ${rangeEnd.toISOString().split('T')[0]}: ${rangeOrders.length} orders, revenue: ${rangeRevenue}`
      );

      return {
        revenue: rangeRevenue,
        orders: rangeOrders.length,
        customers: new Set(rangeOrders.map((order: any) => order.customer_id)).size,
      };
    };
    console.log('period<<<<<<<<<>>>>>', period);
    // Generate trend data based on period type
    switch (period) {
      case 'yearly': {
        const currentYear = new Date().getFullYear();
        for (let i = 4; i >= 0; i--) {
          const year = currentYear - i;
          const yearStart = new Date(year, 0, 1);
          yearStart.setUTCHours(0, 0, 0, 0);
          const yearEnd = new Date(year, 11, 31);
          yearEnd.setUTCHours(23, 59, 59, 999);

          const yearData = calculateRevenueForRange(yearStart, yearEnd);
          revenueTrend.push({
            date: year.toString(),
            ...yearData,
          });
        }
        break;
      }

      case 'quarterly': {
        const currentDate = new Date();
        for (let i = 3; i >= 0; i--) {
          const quarterDate = new Date(currentDate);
          quarterDate.setMonth(currentDate.getMonth() - i * 3);

          const year = quarterDate.getFullYear();
          const quarter = Math.floor(quarterDate.getMonth() / 3) + 1;

          const quarterStart = new Date(year, (quarter - 1) * 3, 1);
          quarterStart.setUTCHours(0, 0, 0, 0);
          const quarterEnd = new Date(year, quarter * 3, 0);
          quarterEnd.setUTCHours(23, 59, 59, 999);

          const quarterData = calculateRevenueForRange(quarterStart, quarterEnd);
          revenueTrend.push({
            date: `${year}-Q${quarter}`,
            ...quarterData,
          });
        }
        break;
      }

      case 'monthly': {
        const currentDate = new Date();

        for (let i = 11; i >= 0; i--) {
          // Create month reference
          const monthDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
          monthDate.setMonth(monthDate.getMonth() - i);

          const year = monthDate.getFullYear();
          const month = monthDate.getMonth();

          // full month boundaries
          const monthStart = new Date(year, month, 1, 0, 0, 0, 0);
          const monthEnd = new Date(year, month + 1, 0, 23, 59, 59, 999);

          const monthData = calculateRevenueForRange(monthStart, monthEnd);

          revenueTrend.push({
            date: `${year}-${String(month + 1).padStart(2, '0')}`,
            ...monthData,
          });
        }
        break;
      }

      case 'weekly': {
        const currentDate = new Date();

        // Generate last 7 days of data
        for (let i = 6; i >= 0; i--) {
          const dayDate = new Date(currentDate);
          dayDate.setDate(currentDate.getDate() - i);

          const dayStart = new Date(dayDate);
          dayStart.setUTCHours(0, 0, 0, 0);

          const dayEnd = new Date(dayDate);
          dayEnd.setUTCHours(23, 59, 59, 999);

          const dayData = calculateRevenueForRange(dayStart, dayEnd);
          revenueTrend.push({
            date: dayStart.toISOString().split('T')[0], // Format: "YYYY-MM-DD"
            ...dayData,
          });
        }
        break;
      }

      // Legacy support for existing periods (daily aggregation)
      default: {
        const days = Math.min(periodDays, 30); // Limit to 30 data points for performance

        for (let i = days - 1; i >= 0; i--) {
          const date = new Date();
          date.setDate(date.getDate() - i);

          // Create separate date objects using UTC to avoid timezone issues
          const dayStart = new Date(date);
          dayStart.setUTCHours(0, 0, 0, 0);

          const dayEnd = new Date(date);
          dayEnd.setUTCHours(23, 59, 59, 999);

          const dayData = calculateRevenueForRange(dayStart, dayEnd);
          revenueTrend.push({
            date: dayStart.toISOString().split('T')[0], // Format: "YYYY-MM-DD"
            ...dayData,
          });
        }
        break;
      }
    }

    // Calculate top products for the tenant (latest 5 products with relevant details)
    const productSales = new Map<string, { revenue: number; units: number; product: any }>();

    orders.forEach((order: any) => {
      order.items?.forEach((item: any) => {
        const productId = item.variant?.product_id;
        if (productId) {
          const existing = productSales.get(productId) || {
            revenue: 0,
            units: 0,
            product: item.variant.product,
          };
          const unitPrice =
            typeof item.unit_price === 'object' && item.unit_price?.value
              ? parseFloat(item.unit_price.value.toString())
              : parseFloat(item.unit_price?.toString() || '0');
          existing.revenue += unitPrice * (item.quantity || 0);
          existing.units += item.quantity || 0;
          productSales.set(productId, existing);
        }
      });
    });

    // Get top 5 products by revenue for the tenant
    const topProducts: TopProduct[] = Array.from(productSales.entries())
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, 5) // Return latest 5 products as requested
      .map(([productId, data]) => ({
        productId,
        title: data.product?.title || 'Unknown Product',
        handle: data.product?.handle || '',
        sku: data.product?.variants?.[0]?.sku || '',
        revenue: data.revenue,
        units: data.units,
        stock: data.product?.variants?.[0]?.inventory_quantity || 0,
        gross: data.revenue,
        price: data.product?.variants?.[0]?.metadata?.original_price || 0,
        sale_price: data.product?.variants?.[0]?.metadata?.sale_price || 0,
        inventory_status:
          data.product?.variants?.[0]?.metadata?.product_inventory_status || 'in_stock',
      }));

    // If no products from orders, get latest 5 products from the tenant's product catalog
    if (topProducts.length === 0 && products.length > 0) {
      const latestProducts = products
        .sort(
          (a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )
        .slice(0, 5)
        .map((product: any) => {
          const firstVariant = product.variants?.[0];

          return {
            productId: product.id,
            title: product.title || 'Unknown Product',
            handle: product.product_handle || product.handle || '',
            sku: firstVariant?.sku || '',
            revenue: 0,
            units: 0,
            stock: product.product_quantity || 0,
            gross: 0,
            price: product.original_price || 0,
            sale_price: product.sale_price || 0,
            inventory_status: product.product_inventory_status || 'in_stock',
          };
        });
      topProducts.push(...latestProducts);
    }

    // Get latest 5 orders for the tenant with relevant order details
    const topOrders: RecentOrder[] = orders.slice(0, 5).map((order: any) => {
      let orderTotal = 0;

      // Use consistent parsing logic
      let totals: any = {};
      if (order.totals) {
        try {
          totals = typeof order.totals === 'string' ? JSON.parse(order.totals) : order.totals;
        } catch (e) {
          console.warn(`Failed to parse totals JSON for top order ${order.id}:`, e);
          totals = {};
        }
      }

      orderTotal = totals.current_order_total || 0;

      return {
        order_id: order.id,
        order_display_id: order.display_id?.toString() || order.id.slice(-6),
        customer_name:
          `${order.customer_first_name || ''} ${order.customer_last_name || ''}`.trim() || 'Guest',
        customer_email: order.customer_email || order.email || '',
        total_order_amount: orderTotal,
        order_status: order.status || 'pending',
        created_at: order.created_at,
      };
    });

    // Calculate refund rate for tenant
    const completedOrders = orders.filter((order: any) => order.status === 'completed');
    const refundedOrders = orders.filter(
      (order: any) => order.status === 'canceled' || order.status === 'refunded'
    );
    const refundRateData = [
      { name: 'Completed', value: completedOrders.length, color: '#10B981' },
      { name: 'Refunded', value: refundedOrders.length, color: '#EF4444' },
    ];

    // Customer segmentation for tenant (simplified based on actual customer data)
    const customerSplit = [
      { segment: 'New Customers', count: Math.floor(totalCustomers * 0.4), percentage: 40 },
      { segment: 'Repeat Customers', count: Math.floor(totalCustomers * 0.35), percentage: 35 },
      { segment: 'VIP Customers', count: Math.floor(totalCustomers * 0.25), percentage: 25 },
    ];

    console.log('📊 Analytics calculated for tenant:', {
      tenantId,
      totalRevenue,
      totalOrders,
      totalCustomers,
      totalProducts,
      averageOrderValue,
      revenueGrowth,
      orderGrowth,
      topProductsCount: topProducts.length,
      topOrdersCount: topOrders.length,
      revenueTrendPoints: revenueTrend.length,
    });

    // Debug revenue trend data
    console.log(
      '📈 Revenue trend data:',
      revenueTrend.map(trend => ({
        date: trend.date,
        revenue: trend.revenue,
        orders: trend.orders,
        customers: trend.customers,
      }))
    );

    // Build response with multi-tenant dashboard analytics
    const analytics: DashboardAnalytics = {
      stats: {
        totalRevenue,
        totalOrders,
        totalCustomers,
        totalProducts,
        averageOrderValue,
        conversionRate: 3.2, // This would need web analytics integration
        revenueGrowth,
        orderGrowth,
        customerGrowth: 0, // Would need historical customer data
      },
      revenueTrend, // Revenue chart data in exact format: [{date: "YYYY-MM-DD", revenue: number, orders: number}, ...]
      topProducts, // Latest 5 products for the tenant
      topOrders, // Latest 5 orders for the tenant
      refundRate: refundRateData,
      customerSplit,
    };

    console.log('✅ Dashboard analytics response prepared for tenant:', tenantId);
    console.log('📊 Final analytics summary:', {
      totalRevenue: analytics.stats.totalRevenue,
      totalOrders: analytics.stats.totalOrders,
      revenueTrendDataPoints: analytics.revenueTrend.length,
      hasRevenueTrendData: analytics.revenueTrend.some(
        trend => trend.revenue > 0 || trend.orders > 0
      ),
    });

    // Set cache-control headers to prevent HTTP 304 responses
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('ETag', ''); // Clear any ETag header
    res.setHeader('Last-Modified', ''); // Clear any Last-Modified header

    res.status(200).json(analytics);
  } catch (error) {
    console.error('❌ Dashboard analytics error:', error);
    res.status(500).json({
      error: 'Failed to fetch dashboard analytics',
      details: error instanceof Error ? error.message : 'Unknown error',
      tenant_id: (req as any).tenantId || 'unknown',
    });
  }
}
