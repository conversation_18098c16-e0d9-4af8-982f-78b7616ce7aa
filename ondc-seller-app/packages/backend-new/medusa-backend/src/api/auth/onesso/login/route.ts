/**
 * OneSSO Authentication - Login Endpoint
 *
 * Implements /auth/onesso/login endpoint
 * Supports both password and OTP-based authentication
 */

import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { hybridAuthService } from '../../../../services/hybrid-auth';
import { z } from 'zod';

// Validation schema for login request
const loginSchema = z
  .object({
    username: z.string().min(1, 'Username is required'),
    password: z.string().optional(),
    login_otp: z.string().optional(),
    remember_me: z.boolean().optional().default(false),
    user_type: z.enum(['admin', 'customer']).optional().default('customer'),
  })
  .refine(data => data.password || data.login_otp, {
    message: 'Either password or login_otp must be provided',
    path: ['password', 'login_otp'],
  });

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log('🔐 [ONESSO-AUTH] Login request received');
    console.log('   Request body:', JSON.stringify(req.body, null, 2));

    // Validate request body
    const validationResult = loginSchema.safeParse(req.body);
    if (!validationResult.success) {
      console.log('❌ [ONESSO-AUTH] Validation failed:', validationResult.error.errors);
      return res.status(422).json({
        success: false,
        message: 'Validation error',
        data: {
          errors: validationResult.error.flatten().fieldErrors,
        },
      });
    }

    const { username, password, login_otp, remember_me, user_type } = validationResult.data;

    // Attempt OneSSO authentication
    try {
      const loginData = {
        username,
        password,
        remember_me,
        user_type,
        ...(login_otp && { login_otp }),
      };

      console.log('🔐 [ONESSO-AUTH] Attempting OneSSO authentication for user:', username);

      // Set container and tenant ID for user synchronization
      if (req.scope) {
        hybridAuthService.setContainer(req.scope);
      }

      // Set tenant ID from request headers
      const tenantId = (req.headers['x-tenant-id'] as string) || (req as any).tenantId || 'default';
      hybridAuthService.setTenantId(tenantId);
      console.log('🏢 [ONESSO-AUTH] Using tenant ID:', tenantId);

      const authResult = await hybridAuthService.login(loginData);

      console.log('✅ [ONESSO-AUTH] OneSSO authentication successful');

      // Return successful authentication response with Medusa integration
      return res.status(200).json({
        success: true,
        message: 'Authentication successful',
        data: {
          user: {
            user_id: authResult.user.user_id,
            user_name: authResult.user.username,
            first_name: authResult.user.first_name,
            last_name: authResult.user.last_name,
            email: authResult.user.email,
            mobile: authResult.user.mobile,
          },
          tokens: {
            access_token: authResult.token || authResult.access_token,
            refresh_token: authResult.refresh_token || null,
            medusa_token: authResult.medusa_auth?.token || null,
          },
          old_sso_tokens: authResult.keycloak_data?.old_sso_tokens || null,
          // token_type: 'Bearer',
          // auth_type: authResult.auth_type || 'onesso',
          // keycloak_synced: authResult.user_sync?.synced || false,
          // expires_in: authResult.expires_in || 3600,
          // user_details_sync: {
          //   details_verified: authResult.user_sync?.synced || false,
          //   details_synced: authResult.user_sync?.synced || false,
          //   sync_message: authResult.user_sync?.synced
          //     ? `User ${authResult.user_sync.action} successfully`
          //     : 'User synchronization failed',
          // },
        },
      });
    } catch (authError: any) {
      console.error('❌ [ONESSO-AUTH] OneSSO authentication failed:', authError.message);

      // Return authentication failure
      return res.status(400).json({
        success: false,
        message: authError.message || 'Authentication failed',
        data: {
          errors: authError.response?.data?.data?.errors || null,
        },
      });
    }
  } catch (error) {
    console.error('❌ [ONESSO-AUTH] Login error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      data: null,
    });
  }
}

// GET endpoint for authentication status
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log('ℹ️ [ONESSO-AUTH] Login status request');

    return res.status(200).json({
      success: true,
      message: 'OneSSO login endpoint is available',
      data: {
        endpoint: '/auth/onesso/login',
        methods: ['POST'],
        description: 'User login with password or OTP via OneSSO',
        auth_methods: ['password', 'otp'],
        required_fields: ['username'],
        optional_fields: ['password', 'login_otp', 'remember_me'],
      },
    });
  } catch (error) {
    console.error('❌ [ONESSO-AUTH] Status error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      data: null,
    });
  }
}
