/**
 * OneSSO Authentication - Registration Endpoint
 *
 * Implements /auth/onesso/register endpoint
 * Registers new users in OneSSO system
 */

import { MedusaRequest, MedusaResponse } from '@medusajs/framework/http';
import { hybridAuthService } from '../../../../services/hybrid-auth';
import { z } from 'zod';

// Validation schema for registration request
const registerSchema = z
  .object({
    password: z.string().min(6, 'Password must be at least 6 characters'),
    password_confirmation: z.string().optional(),
    email: z.string().email('Invalid email format'),
    mobile: z.string().min(10, 'Mobile number must be at least 10 digits'),
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    store_name: z.string().min(1, 'Store name is required'),
    store_handle: z
      .string()
      .min(1, 'Store handle is required')
      .max(50, 'Store handle must be less than 50 characters')
      .regex(
        /^[a-z0-9-]+$/,
        'Store handle must contain only lowercase letters, numbers, and hyphens'
      ),
    user_type: z.enum(['customer', 'admin']).default('admin'),
  })
  .refine(data => !data.password_confirmation || data.password === data.password_confirmation, {
    message: 'Password confirmation does not match password',
    path: ['password_confirmation'],
  });

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log('[ONESSO-AUTH] Registration request received');
    console.log('   Request body:', JSON.stringify(req.body, null, 2));

    // Validate request body
    const validationResult = registerSchema.safeParse(req.body);

    if (!validationResult.success) {
      console.log('[ONESSO-AUTH] Validation failed:', validationResult.error.errors);
      return res.status(422).json({
        success: false,
        message: 'Validation error',
        data: {
          errors: validationResult.error.flatten().fieldErrors,
        },
      });
    }

    const {
      password,
      email,
      mobile,
      first_name,
      last_name,
      password_confirmation,
      store_handle,
      store_name,
      user_type,
    } = validationResult.data;

    // Attempt OneSSO registration
    try {
      const registrationData = {
        password,
        email,
        mobile,
        first_name,
        last_name,
        password_confirmation,
        store_handle,
        store_name,
        user_type,
      };

      console.log('[ONESSO-AUTH] Attempting OneSSO registration for user:', email);

      const registrationResult = await hybridAuthService.register(registrationData);

      console.log('[ONESSO-AUTH] OneSSO registration successful');

      // Return successful registration response with Medusa integration
      return res.status(201).json({
        success: true,
        message: registrationResult.combined_success
          ? 'Registration successful with Medusa admin signup'
          : 'OneSSO registration successful',
        data: {
          user: registrationResult.data?.user || registrationResult.user,
          registration_type: registrationResult.data?.registration_type || 'onesso',
          keycloak_synced: registrationResult.data?.keycloak_synced || false,
          medusa_integration: {
            enabled: true,
            admin_signup_attempted: !!registrationResult.medusa_signup,
            admin_signup_successful: registrationResult.medusa_signup?.success || false,
            api_call: registrationResult.medusa_signup?.api_call || null,
            api_status: registrationResult.medusa_signup?.status || null,
            api_response: registrationResult.medusa_signup?.data || null,
            admin_user: registrationResult.medusa_signup?.data?.user || null,
            store_info: registrationResult.medusa_signup?.data?.store || null,
            error: registrationResult.medusa_signup?.error || null,
            message: registrationResult.medusa_signup?.success
              ? 'Medusa admin signup successful'
              : registrationResult.medusa_signup?.error || 'Medusa admin signup not attempted',
          },
          request_opt: registrationResult.requestOtpResponse,
          combined_success: registrationResult.combined_success || false,
        },
      });
    } catch (authError: any) {
      console.error('[ONESSO-AUTH] OneSSO registration failed:', authError.message);

      // Check for known database schema issue
      const isSchemaIssue =
        authError.message && authError.message.includes("Unknown column 'email_id'");

      if (isSchemaIssue) {
        console.error('🚨 [ONESSO-AUTH] Known issue: External service database schema mismatch');
        console.error(
          "   Issue: Validation rule expects 'email_id' column but database has 'email' column"
        );
        console.error('   Service: http://13.200.76.240:8000');
        console.error('   Resolution: External service backend team needs to fix database schema');

        return res.status(503).json({
          success: false,
          message:
            'Registration service temporarily unavailable due to external service configuration issue',
          data: {
            error_type: 'external_service_schema_mismatch',
            error_details: 'Database schema inconsistency in external authentication service',
            resolution_required: 'External service team needs to fix database column mapping',
            contact_support: true,
            technical_details: {
              service_url: 'http://13.200.76.240:8000',
              issue: 'Validation rule references non-existent email_id column',
              expected_column: 'email',
              actual_error: authError.message,
            },
          },
        });
      }

      // Return other registration failures
      return res.status(400).json({
        success: false,
        message: authError.message || 'Registration failed',
        data: {
          errors: authError.response?.data?.data?.errors || null,
        },
      });
    }
  } catch (error) {
    console.error('[ONESSO-AUTH] Registration error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      data: null,
    });
  }
}

// GET endpoint for registration status
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log('[ONESSO-AUTH] Registration status request');

    return res.status(200).json({
      success: true,
      message: 'OneSSO registration endpoint is available',
      data: {
        endpoint: '/auth/onesso/register',
        methods: ['POST'],
        description: 'Register new user in OneSSO authentication system',
        required_fields: ['password', 'email', 'mobile', 'first_name', 'last_name'],
        optional_fields: ['password_confirmation'],
        validation_rules: {
          password: 'Minimum 6 characters',
          email: 'Valid email format required',
          mobile: 'Minimum 10 digits',
        },
      },
    });
  } catch (error) {
    console.error('[ONESSO-AUTH] Status error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      data: null,
    });
  }
}
