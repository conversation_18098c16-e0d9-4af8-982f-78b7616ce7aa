/**
 * Hybrid Authentication Service
 *
 * Provides unified authentication interface that can work with:
 * - Keycloak Authentication Service (primary)
 * - Medusa Default Authentication (fallback)
 * - Development mode authentication
 * - Automatic user synchronization to Medusa database
 */

import { keycloakAuthService, KeycloakLoginRequest, KeycloakLoginResponse } from './keycloak-auth';
import { authConfigService } from './auth-config';
import { createUserSyncService, KeycloakUserData, SyncResult } from './user-sync';
import { medusaAuthService, MedusaLoginRequest } from './medusa-auth';
import axios, { AxiosResponse } from 'axios';

export interface HybridLoginRequest {
  email?: string;
  username?: string;
  password?: string;
  login_otp?: string;
  remember_me?: boolean;
  user_type?: 'admin' | 'customer';
}

export interface HybridLoginResponse {
  success: boolean;
  token: string;
  access_token?: string;
  refresh_token?: string;
  user?: any;
  auth_type: 'keycloak' | 'medusa' | 'dev';
  keycloak_data?: KeycloakLoginResponse['data'];
  expires_in?: number;
  user_sync?: {
    synced: boolean;
    user_id?: string;
    customer_id?: string;
    action?: 'created' | 'updated' | 'found';
  };
  medusa_auth?: {
    success: boolean;
    token?: string;
    customer?: any;
    error?: string;
  };
}

export interface HybridRegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  mobile?: string;
  store_name?: string;
  store_handle?: string;
  user_type?: string;
}

export interface HybridRegisterResponse {
  success: boolean;
  user: any;
  auth_type: 'keycloak' | 'medusa';
  message?: string;
}

export class HybridAuthService {
  private container?: any;
  private tenantId?: string;
  private medusaAuthApiUrl: string;
  private medusaAdminSignupApiUrl: string;

  constructor() {
    console.log('[HYBRID-AUTH] Initializing hybrid authentication service');
    console.log(`   Auth Mode: ${authConfigService.getAuthMode()}`);
    console.log(`   Keycloak Enabled: ${authConfigService.isKeycloakEnabled()}`);
    console.log(`   Medusa Enabled: ${authConfigService.isMedusaEnabled()}`);
    console.log(`   Dev Mode: ${authConfigService.isDevModeEnabled()}`);

    // Load Medusa API URLs from environment
    this.medusaAuthApiUrl =
      process.env.MEDUSA_AUTH_API_URL || 'http://localhost:9000/auth/user/emailpass';
    this.medusaAdminSignupApiUrl =
      process.env.MEDUSA_ADMIN_SIGNUP_API_URL || 'http://localhost:9000/public/admin-signup';
    console.log(`   Medusa Auth API URL: ${this.medusaAuthApiUrl}`);
    console.log(`   Medusa Admin Signup API URL: ${this.medusaAdminSignupApiUrl}`);
  }

  /**
   * Set the Medusa container for dependency injection
   */
  setContainer(container: any): void {
    this.container = container;
    // Also set container for Medusa auth service
    medusaAuthService.setContainer(container);
  }

  /**
   * Set the tenant ID for multi-tenant operations
   */
  setTenantId(tenantId: string): void {
    this.tenantId = tenantId;
  }

  /**
   * Make HTTP API call to Medusa admin signup endpoint after OneSSO registration success
   */
  private async callMedusaAdminSignupAPI(userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    storeName?: string;
    storeHandle?: string;
    userType?: string;
  }): Promise<any> {
    try {
      console.log('[HYBRID-AUTH] Making HTTP API call to Medusa admin signup endpoint');
      console.log('[HYBRID-AUTH] Admin Signup API Call Details:', {
        url: this.medusaAdminSignupApiUrl,
        method: 'POST',
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        storeName: userData.storeName,
        storeHandle: userData.storeHandle,
        password: '[REDACTED]',
        source: 'environment variable MEDUSA_ADMIN_SIGNUP_API_URL',
      });

      const payload = {
        email: userData.email,
        password: userData.password,
        firstName: userData.firstName,
        lastName: userData.lastName,
        userType: userData.userType,
        storeName: userData.storeName || `${userData.firstName} ${userData.lastName} Store`,
        storeHandle:
          userData.storeHandle ||
          `${userData.firstName.toLowerCase()}-${userData.lastName.toLowerCase()}-store`,
      };

      const response: AxiosResponse = await axios.post(this.medusaAdminSignupApiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      console.log('[HYBRID-AUTH] Medusa admin signup API call successful');
      console.log('[HYBRID-AUTH] Admin Signup API Response Status:', response.status);
      console.log('[HYBRID-AUTH] Admin Signup API Response Data:', response.data);

      return {
        success: true,
        status: response.status,
        data: response.data,
        api_call: this.medusaAdminSignupApiUrl,
        message: 'Medusa admin signup API call successful',
      };
    } catch (error: any) {
      console.error('[HYBRID-AUTH] Medusa admin signup API call failed:', error.message);
      console.error('[HYBRID-AUTH] Admin Signup API Error Details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
        url: this.medusaAdminSignupApiUrl,
      });

      return {
        success: false,
        status: error.response?.status || 500,
        error: error.response?.data || error.message,
        api_call: this.medusaAdminSignupApiUrl,
        message: 'Medusa admin signup API call failed',
      };
    }
  }

  /**
   * Make HTTP API call to Medusa auth endpoint after Keycloak success
   */
  private async callMedusaAuthAPI(email: string, password: string): Promise<any> {
    try {
      console.log('[HYBRID-AUTH] Making HTTP API call to Medusa auth endpoint');
      console.log('[HYBRID-AUTH] API Call Details:', {
        url: this.medusaAuthApiUrl,
        method: 'POST',
        email: email,
        password: '[REDACTED]',
        source: 'environment variable MEDUSA_AUTH_API_URL',
      });

      const payload = {
        email: email,
        password: password,
      };

      const response: AxiosResponse = await axios.post(this.medusaAuthApiUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      });

      console.log('[HYBRID-AUTH] Medusa auth API call successful');
      console.log('[HYBRID-AUTH] API Response Status:', response.status);
      console.log('[HYBRID-AUTH] API Response Data:', response.data);

      return {
        success: true,
        status: response.status,
        data: response.data,
        api_call: this.medusaAuthApiUrl,
        message: 'Medusa auth API call successful',
      };
    } catch (error: any) {
      console.error('[HYBRID-AUTH] Medusa auth API call failed:', error.message);
      console.error('[HYBRID-AUTH] API Error Details:', {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message,
        url: this.medusaAuthApiUrl,
      });

      return {
        success: false,
        status: error.response?.status || 500,
        error: error.response?.data || error.message,
        api_call: this.medusaAuthApiUrl,
        message: 'Medusa auth API call failed',
      };
    }
  }

  /**
   * Unified login method that handles multiple authentication sources
   */
  async login(credentials: HybridLoginRequest): Promise<HybridLoginResponse> {
    console.log('[HYBRID-AUTH] Processing login request');

    const keycloakResult = await this.handleKeycloakLogin(credentials);
    if (keycloakResult) {
      return keycloakResult;
    }

    // Fallback to Medusa authentication (if enabled)
    if (authConfigService.isMedusaEnabled()) {
      try {
        return await this.handleMedusaLogin(credentials);
      } catch (error) {
        console.error('[HYBRID-AUTH] Medusa login failed:', error);
        throw error;
      }
    }

    throw new Error('No authentication method available');
  }

  /**
   * Handle development mode authentication with real Keycloak credentials
   */
  private async handleDevModeLogin(
    credentials: HybridLoginRequest
  ): Promise<HybridLoginResponse | null> {
    const devCredentials = authConfigService.getDevModeCredentials();
    const username = credentials.username || credentials.email;

    // Real Keycloak credentials for testing
    const realKeycloakCredentials = {
      username: '919875896982',
      password: 'Admin@123',
    };

    // Check for real Keycloak credentials first
    const isRealKeycloakLogin =
      username === realKeycloakCredentials.username &&
      credentials.password === realKeycloakCredentials.password;

    // Check for dev mode credentials (both username and email formats)
    const isDevModeLogin =
      (username === devCredentials.username && credentials.password === devCredentials.password) ||
      (credentials.email === '<EMAIL>' &&
        credentials.password === devCredentials.password) ||
      (credentials.email === '<EMAIL>' &&
        credentials.password === devCredentials.password);

    if (isRealKeycloakLogin) {
      console.log('[HYBRID-AUTH] Real Keycloak credentials authentication successful');

      return {
        success: true,
        token: this.generateDevModeToken(),
        auth_type: 'keycloak',
        user: {
          id: 'keycloak-user-919875896982',
          email: '<EMAIL>',
          username: realKeycloakCredentials.username,
          first_name: 'Keycloak',
          last_name: 'User',
        },
        expires_in: 3600,
      };
    }

    if (isDevModeLogin) {
      console.log('[HYBRID-AUTH] Development mode authentication successful');

      return {
        success: true,
        token: this.generateDevModeToken(),
        auth_type: 'dev',
        user: {
          id: 'dev-user-001',
          email: '<EMAIL>',
          username: 'demo',
          first_name: 'Demo',
          last_name: 'User',
        },
        expires_in: 3600,
      };
    }

    return null;
  }

  /**
   * Handle Keycloak authentication
   */
  private async handleKeycloakLogin(
    credentials: HybridLoginRequest
  ): Promise<HybridLoginResponse | null> {
    console.log('[HYBRID-AUTH] Attempting Keycloak authentication');

    const keycloakRequest: KeycloakLoginRequest = {
      username: credentials.username || credentials.email || '',
      password: credentials.password,
      remember_me: credentials.remember_me,
    };

    try {
      const keycloakResponse = await keycloakAuthService.login(keycloakRequest);

      if (keycloakResponse.success) {
        console.log('[HYBRID-AUTH] Keycloak authentication successful');

        // Prepare user sync data
        let userSyncResult: SyncResult | null = null;
        let tenantId = this.tenantId || 'default';

        if (this.container) {
          try {
            const userSyncService = createUserSyncService(this.container);
            // Get tenant ID from multiple sources
            if (!tenantId || tenantId === 'default') {
              try {
                tenantId = (this.container.resolve('tenantId') as string) || tenantId;
              } catch (error) {
                // Try to get tenant ID from request context
                try {
                  const req = this.container.resolve('req');
                  tenantId = req?.headers?.['x-tenant-id'] || req?.tenantId || tenantId;
                } catch (reqError) {
                  console.warn(
                    '[HYBRID-AUTH] Could not resolve tenantId from container or request, using:',
                    tenantId
                  );
                }
              }
            }

            console.log('[HYBRID-AUTH] Using tenant ID for user sync:', tenantId);

            // Extract user data from Keycloak response
            const keycloakUserData: KeycloakUserData = {
              id: keycloakResponse.data.user?.user_id,
              email: keycloakResponse.data.user?.email,
              username: keycloakResponse.data.user?.username,
              first_name: keycloakResponse.data.user?.first_name,
              last_name: keycloakResponse.data.user?.last_name,
              phone: keycloakResponse.data.user?.mobile,
              mobile: keycloakResponse.data.user?.mobile,
              user_type: credentials.user_type || 'customer',
              tenant_id: tenantId,
            };

            userSyncResult = await userSyncService.syncUser(keycloakUserData, tenantId);
            console.log('[HYBRID-AUTH] User synchronization successful:', userSyncResult);
          } catch (syncError) {
            console.error('[HYBRID-AUTH] User synchronization failed:', syncError);
            // Don't fail the login if sync fails, just log the error
          }
        }

        // Make HTTP API call to Medusa auth endpoint after successful Keycloak login
        let medusaAuthResult = null;
        if (keycloakResponse.data.user?.email && keycloakRequest.password) {
          try {
            console.log('[HYBRID-AUTH] Triggering Medusa auth API call after Keycloak success');

            // Make HTTP API call to http://localhost:9000/auth/user/emailpass
            const medusaApiResult = await this.callMedusaAuthAPI(
              keycloakResponse.data.user.email,
              keycloakRequest.password
            );

            if (medusaApiResult.success) {
              console.log('[HYBRID-AUTH] Medusa auth API call successful');

              medusaAuthResult = {
                success: true,
                api_response: medusaApiResult.data,
                api_status: medusaApiResult.status,
                api_call: medusaApiResult.api_call,
                token: medusaApiResult.data?.token || medusaApiResult.data?.access_token,
                user: medusaApiResult.data?.user || medusaApiResult.data?.customer,
                message: 'Medusa auth API call successful',
              };
            } else {
              console.warn('[HYBRID-AUTH] Medusa auth API call failed');

              medusaAuthResult = {
                success: false,
                api_response: medusaApiResult.error,
                api_status: medusaApiResult.status,
                api_call: medusaApiResult.api_call,
                error: medusaApiResult.message || 'Medusa auth API call failed',
              };
            }
          } catch (medusaError) {
            console.error('[HYBRID-AUTH] Medusa auth API call error:', medusaError);
            medusaAuthResult = {
              success: false,
              error:
                medusaError instanceof Error ? medusaError.message : 'Medusa auth API call error',
            };
          }
        } else {
          console.warn('[HYBRID-AUTH] Missing email or password for Medusa auth API call');
          medusaAuthResult = {
            success: false,
            error: 'Missing email or password for Medusa auth API call',
          };
        }

        return {
          success: true,
          token: keycloakResponse.data.tokens.access_token,
          access_token: keycloakResponse.data.tokens.access_token,
          refresh_token: keycloakResponse.data.tokens.refresh_token,
          auth_type: 'keycloak',
          user: keycloakResponse.data.user,
          keycloak_data: keycloakResponse.data,
          expires_in: keycloakResponse.data.tokens.expires_in,
          user_sync: userSyncResult
            ? {
                synced: userSyncResult.success,
                user_id: userSyncResult.user_id,
                customer_id: userSyncResult.customer_id,
                action: userSyncResult.action,
              }
            : { synced: false },
          medusa_auth: medusaAuthResult,
        };
      }
    } catch (error) {
      console.error('[HYBRID-AUTH] Keycloak authentication error:', error);
      throw error;
    }

    return null;
  }

  /**
   * Handle Medusa default authentication
   */
  private async handleMedusaLogin(credentials: HybridLoginRequest): Promise<HybridLoginResponse> {
    console.log('[HYBRID-AUTH] Attempting Medusa authentication');

    // This would integrate with Medusa's default auth system
    // For now, we'll simulate the response structure
    throw new Error('Medusa authentication not yet implemented in hybrid service');
  }

  /**
   * Unified registration method with Medusa admin signup integration
   */
  async register(userData: HybridRegisterRequest): Promise<any> {
    try {
      console.log('[HYBRID-AUTH] Processing registration request with Medusa integration');

      // First, handle OneSSO registration
      const onessoResult = await this.handleKeycloakRegistration(userData);
      console.log('onessoResult:::::', onessoResult);
      if (onessoResult && onessoResult.success) {
        console.log('[HYBRID-AUTH] OneSSO registration successful, triggering Medusa admin signup');

        // Make Medusa admin signup API call after successful OneSSO registration
        let medusaSignupResult = null;
        try {
          medusaSignupResult = await this.callMedusaAdminSignupAPI({
            email: userData.email,
            password: userData.password,
            firstName: userData.first_name,
            lastName: userData.last_name,
            storeName: userData.store_name,
            storeHandle: userData.store_handle,
            userType: userData.user_type,
          });
        } catch (medusaError) {
          console.error('[HYBRID-AUTH] Medusa admin signup failed:', medusaError);
          medusaSignupResult = {
            success: false,
            error:
              medusaError instanceof Error ? medusaError.message : 'Medusa admin signup failed',
          };
        }
        const requestOtpResponse = await this.handleKeycloakRequestOtp(userData.mobile as string);
        console.log('requestOtpResponse::::::::<><><>', requestOtpResponse);
        // Return combined response
        return {
          ...onessoResult,
          medusa_signup: medusaSignupResult,
          combined_success: onessoResult.success && (medusaSignupResult?.success || false),
          requestOtpResponse,
        };
      } else {
        // OneSSO registration failed
        return onessoResult;
      }
    } catch (error) {
      console.error('Registration failed:', error);
      throw error || 'No registration method available';
    }
  }

  /**
   * Unified registration method
   */
  async requestOtp(username: string): Promise<any> {
    try {
      return await this.handleKeycloakRequestOtp(username);
    } catch (error) {
      console.error('Keycloak registration failed:', error);
      throw error || 'No registration method available';
    }
  }

  /**
   * Unified verify otp method
   */
  async verifyOtp(username: string, otp: string): Promise<any> {
    try {
      return await this.handleKeycloakOtpVerification(username, otp);
    } catch (error) {
      console.error('Keycloak registration failed:', error);
      throw error || 'No registration method available';
    }
  }

  /**
   * Handle Keycloak registration
   */
  private async handleKeycloakRegistration(userData: HybridRegisterRequest): Promise<any | null> {
    try {
      const keycloakData = {
        email: userData.email,
        password: userData.password,
        password_confirmation: userData.password, // Required by Keycloak API
        first_name: userData.first_name,
        last_name: userData.last_name,
        mobile: userData.mobile || '',
      };

      const keycloakResponse = await keycloakAuthService.register(keycloakData);

      if (keycloakResponse.success) {
        console.log('[HYBRID-AUTH] Keycloak registration successful');

        return keycloakResponse;
      }
    } catch (error) {
      console.error('[HYBRID-AUTH] Keycloak registration error:', error);
      throw error;
    }
  }

  /**
   * Handle Keycloak request OTP
   */
  private async handleKeycloakRequestOtp(userName: string): Promise<any | null> {
    try {
      const keycloakResponse = await keycloakAuthService.requestOtp(userName);

      if (keycloakResponse.success) {
        console.log('[HYBRID-AUTH] Keycloak registration successful');

        return keycloakResponse;
      }
    } catch (error) {
      console.error('[HYBRID-AUTH] Keycloak registration error:', error);
      throw error;
    }
  }

  /**
   * Handle Keycloak request OTP
   */
  private async handleKeycloakOtpVerification(username: string, otp: string): Promise<any | null> {
    try {
      const keycloakResponse = await keycloakAuthService.verifyMobileOtp(username, otp);

      if (keycloakResponse.success) {
        console.log('[HYBRID-AUTH] Keycloak registration successful');

        return keycloakResponse;
      }
    } catch (error) {
      console.error('[HYBRID-AUTH] Keycloak registration error:', error);
      throw error;
    }
  }

  /**
   * Handle Medusa registration
   */
  private async handleMedusaRegistration(
    userData: HybridRegisterRequest
  ): Promise<HybridRegisterResponse> {
    console.log('[HYBRID-AUTH] Attempting Medusa registration');

    // This would integrate with Medusa's default registration system
    throw new Error('Medusa registration not yet implemented in hybrid service');
  }

  /**
   * Validate token from any authentication source
   */
  async validateToken(token: string): Promise<{ valid: boolean; user?: any; auth_type?: string }> {
    console.log('[HYBRID-AUTH] Validating token');

    // Check if it's a development mode token
    if (authConfigService.isDevModeEnabled() && this.isDevModeToken(token)) {
      return {
        valid: true,
        user: {
          id: 'dev-user-001',
          email: '<EMAIL>',
          username: 'demo',
        },
        auth_type: 'dev',
      };
    }

    // Try Keycloak token validation
    if (
      authConfigService.isKeycloakEnabled() &&
      authConfigService.isKeycloakTokenValidationEnabled()
    ) {
      try {
        const validationResult = await keycloakAuthService.validateToken(token);
        if (validationResult.valid) {
          return {
            valid: true,
            auth_type: 'keycloak',
            user: {
              id: validationResult.user?.old_sso_user_id || validationResult.user?.sub,
              email: validationResult.user?.email,
              username: validationResult.user?.preferred_username,
              first_name: validationResult.user?.given_name,
              last_name: validationResult.user?.family_name,
              user_type: 'admin', // Default to admin for now
              tenant_id: 'default',
            },
          };
        }
      } catch (error) {
        console.warn('[HYBRID-AUTH] Keycloak token validation failed:', error);
      }
    }

    // Try Medusa token validation
    if (authConfigService.isMedusaEnabled()) {
      // This would integrate with Medusa's token validation
      // For now, assume valid if we reach here
      return {
        valid: true,
        auth_type: 'medusa',
      };
    }

    return { valid: false };
  }

  /**
   * Refresh token
   */
  async refreshToken(
    refreshToken: string,
    keycloakRefreshToken?: string
  ): Promise<HybridLoginResponse> {
    console.log('[HYBRID-AUTH] Refreshing token');

    if (authConfigService.isKeycloakEnabled() && authConfigService.isAutoRefreshEnabled()) {
      try {
        const refreshResponse = await keycloakAuthService.refreshToken(
          refreshToken,
          keycloakRefreshToken
        );

        if (refreshResponse.success) {
          const tokens = refreshResponse.data.keycloak || refreshResponse.data.old_sso;

          if (tokens) {
            return {
              success: true,
              token: tokens.access_token,
              access_token: tokens.access_token,
              refresh_token: tokens.refresh_token,
              auth_type: 'keycloak',
              expires_in: tokens.expires_in,
            };
          }
        }
      } catch (error) {
        console.error('[HYBRID-AUTH] Token refresh failed:', error);
        throw error;
      }
    }

    throw new Error('Token refresh not available');
  }

  /**
   * Generate development mode token
   */
  private generateDevModeToken(): string {
    const payload = {
      user_id: 'dev-user-001',
      username: 'demo',
      email: '<EMAIL>',
      auth_type: 'dev',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };

    // Simple base64 encoding for dev mode (not secure, just for development)
    return Buffer.from(JSON.stringify(payload)).toString('base64');
  }

  /**
   * Check if token is a development mode token
   */
  private isDevModeToken(token: string): boolean {
    try {
      const decoded = JSON.parse(Buffer.from(token, 'base64').toString());
      return decoded.auth_type === 'dev';
    } catch {
      return false;
    }
  }

  /**
   * Get authentication service health status
   */
  async getHealthStatus(): Promise<{
    keycloak: boolean;
    medusa: boolean;
    medusa_auth: boolean;
    overall: boolean;
  }> {
    const status = {
      keycloak: false,
      medusa: true, // Assume Medusa is always available
      medusa_auth: false,
      overall: false,
    };

    if (authConfigService.isKeycloakEnabled()) {
      try {
        status.keycloak = await keycloakAuthService.getHealthStatus();
      } catch (error) {
        console.warn('[HYBRID-AUTH] Keycloak health check failed:', error);
      }
    }

    // Check Medusa authentication service health
    try {
      status.medusa_auth = await medusaAuthService.getHealthStatus();
    } catch (error) {
      console.warn('[HYBRID-AUTH] Medusa auth health check failed:', error);
    }

    status.overall = status.keycloak || status.medusa || status.medusa_auth;

    return status;
  }
}

// Export singleton instance
export const hybridAuthService = new HybridAuthService();
