/**
 * Keycloak Authentication Service
 *
 * Integrates with OneFoodDialer 2025 Authentication Service API
 * Provides hybrid authentication (Old SSO + Keycloak)
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Types based on OpenAPI specification
export interface KeycloakLoginRequest {
  username: string;
  password?: string;
  login_otp?: string;
  remember_me?: boolean;
}

export interface KeycloakRegisterRequest {
  password: string;
  password_confirmation?: string;
  email: string;
  email_id?: string; // For backend compatibility
  mobile: string;
  first_name: string;
  last_name: string;
}

export interface KeycloakUser {
  user_id: string;
  username: string;
  email: string;
  mobile: string;
  first_name: string;
  last_name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface KeycloakTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  token_type: string;
}

export interface OldSsoTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

export interface KeycloakLoginResponse {
  success: boolean;
  message: string;
  data: {
    user: KeycloakUser;
    tokens: KeycloakTokens;
    old_sso_tokens: OldSsoTokens;
    token_type: string;
    auth_type: string;
    keycloak_synced: boolean;
    expires_in: number;
    user_details_sync: {
      details_verified: boolean;
      details_synced: boolean;
      sync_message: string;
    };
  };
}

export interface KeycloakRegisterResponse {
  success: boolean;
  message: string;
  data: {
    user: KeycloakUser;
    registration_type: string;
    keycloak_synced: boolean;
  };
}

export interface KeycloakRefreshTokenRequest {
  refresh_token: string;
}

export interface KeycloakRefreshTokenResponse {
  success: boolean;
  message: string;
  data: {
    old_sso: OldSsoTokens | null;
    keycloak: KeycloakTokens | null;
  };
}

export interface KeycloakErrorResponse {
  success: false;
  message: string;
  data: {
    errors?: Record<string, string[]>;
  } | null;
}

export class KeycloakAuthService {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor() {
    // Determine which URL to use based on environment
    this.baseUrl = this.getAuthServiceUrl();

    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      config => {
        console.log(`🔐 [KEYCLOAK-AUTH] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      error => {
        console.error('❌ [KEYCLOAK-AUTH] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.client.interceptors.response.use(
      response => {
        console.log(`✅ [KEYCLOAK-AUTH] Response ${response.status} from ${response.config.url}`);
        return response;
      },
      error => {
        console.error(`❌ [KEYCLOAK-AUTH] Response error:`, {
          status: error.response?.status,
          message: error.response?.data?.message || error.message,
          url: error.config?.url,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Determine which authentication service URL to use
   */
  private getAuthServiceUrl(): string {
    const nodeEnv = process.env.NODE_ENV || 'development';

    if (nodeEnv === 'development') {
      return process.env.KEYCLOAK_AUTH_SERVICE_URL || 'http://13.200.76.240:8000';
    } else if (nodeEnv === 'production') {
      return process.env.KEYCLOAK_AUTH_SERVICE_URL || 'http://43.205.191.243:9004/api';
    } else {
      // Use proxy for staging/testing
      return process.env.KEYCLOAK_AUTH_SERVICE_PROXY_URL || 'http://13.200.76.240:8000/api';
    }
  }

  /**
   * Login user with password or OTP
   */
  async login(credentials: KeycloakLoginRequest): Promise<KeycloakLoginResponse> {
    try {
      console.log('🔐 [KEYCLOAK-AUTH] Attempting login for user:', credentials.username);

      const response: AxiosResponse<KeycloakLoginResponse> = await this.client.post(
        '/v2/hybrid-auth/login',
        credentials
      );

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] Login successful for user:', credentials.username);
        return response.data;
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('❌ [KEYCLOAK-AUTH] Login failed:', error.response?.data || error.message);
      throw this.handleError(error);
    }
  }

  /**
   * Register new user
   */
  async register(userData: KeycloakRegisterRequest): Promise<KeycloakRegisterResponse> {
    try {
      console.log('📝 [KEYCLOAK-AUTH] Attempting registration for user:', userData.email);

      // Map email to email_id for external service compatibility
      const requestData = {
        ...userData,
        email_id: userData.email, // Add email_id field for backend compatibility
        // Keep original email field as well in case it's needed
      };

      console.log('📝 [KEYCLOAK-AUTH] Sending registration data:', {
        ...requestData,
        password: '[REDACTED]',
        password_confirmation: '[REDACTED]',
      });

      const response: AxiosResponse<KeycloakRegisterResponse> = await this.client.post(
        '/v2/hybrid-auth/register',
        requestData
      );

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] Registration successful for user:', userData.email);
        return response.data;
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Registration failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Request OTP for login
   */
  async requestOtp(username: string): Promise<any> {
    try {
      console.log('📱 [KEYCLOAK-AUTH] Requesting OTP for user:', username);

      const response = await this.client.post('/v2/hybrid-auth/request-otp', {
        username,
      });
      console.log('otp response <<<<>>>>>', response);
      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] OTP requested successfully for user:', username);
        return response.data;
      } else {
        throw new Error(response.data.message || 'OTP request failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] OTP request failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Verify mobile OTP
   */
  async verifyMobileOtp(username: string, otp: string): Promise<any> {
    try {
      console.log('🔍 [KEYCLOAK-AUTH] Verifying mobile OTP for user:', username);

      const response = await this.client.post('/v2/hybrid-auth/verify-mobile-otp', {
        username,
        otp,
      });

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] Mobile OTP verified successfully for user:', username);
        return response.data;
      } else {
        throw new Error(response.data.message || 'OTP verification failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Mobile OTP verification failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Request OTP for forgot password
   */
  async requestForgotPasswordOtp(username: string): Promise<any> {
    try {
      console.log('🔑 [KEYCLOAK-AUTH] Requesting forgot password OTP for user:', username);

      const response = await this.client.post('/v2/hybrid-auth/forgot-password/request-otp', {
        username,
      });

      if (response.data.success) {
        console.log(
          '✅ [KEYCLOAK-AUTH] Forgot password OTP requested successfully for user:',
          username
        );
        return response.data;
      } else {
        throw new Error(response.data.message || 'Forgot password OTP request failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Forgot password OTP request failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Verify forgot password OTP
   */
  async verifyForgotPasswordOtp(username: string, otp: string): Promise<any> {
    try {
      console.log('🔍 [KEYCLOAK-AUTH] Verifying forgot password OTP for user:', username);

      const response = await this.client.post('/v2/hybrid-auth/forgot-password/verify-otp', {
        username,
        otp,
      });

      if (response.data.success) {
        console.log(
          '✅ [KEYCLOAK-AUTH] Forgot password OTP verified successfully for user:',
          username
        );
        return response.data;
      } else {
        throw new Error(response.data.message || 'Forgot password OTP verification failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Forgot password OTP verification failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Reset password using auth code
   */
  async resetPassword(
    username: string,
    fpAuthCode: string,
    password: string,
    otpType?: string
  ): Promise<any> {
    try {
      console.log('🔄 [KEYCLOAK-AUTH] Resetting password for user:', username);

      const requestData: any = {
        username,
        fp_auth_code: fpAuthCode,
        password,
      };

      if (otpType) {
        requestData.otp_type = otpType;
      }

      const response = await this.client.post(
        '/v2/hybrid-auth/forgot-password/reset-password',
        requestData
      );

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] Password reset successfully for user:', username);
        return response.data;
      } else {
        throw new Error(response.data.message || 'Password reset failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Password reset failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(
    refreshToken: string,
    keycloakRefreshToken?: string
  ): Promise<KeycloakRefreshTokenResponse> {
    try {
      console.log('🔄 [KEYCLOAK-AUTH] Refreshing tokens');

      const headers: Record<string, string> = {};
      if (keycloakRefreshToken) {
        headers['x-refresh-token'] = keycloakRefreshToken;
      }

      const response: AxiosResponse<KeycloakRefreshTokenResponse> = await this.client.post(
        '/v2/hybrid-auth/refresh-token',
        { refresh_token: refreshToken },
        { headers }
      );

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] Token refresh successful');
        return response.data;
      } else {
        throw new Error(response.data.message || 'Token refresh failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Token refresh failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Change password with token fallback
   */
  async changePassword(
    accessToken: string,
    refreshToken: string,
    oldPassword: string,
    newPassword: string
  ): Promise<any> {
    try {
      console.log('🔐 [KEYCLOAK-AUTH] Changing password');

      const response = await this.client.post(
        '/v2/hybrid-auth/change-password',
        {
          access_token: accessToken,
          refresh_token: refreshToken,
          old_password: oldPassword,
          new_password: newPassword,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }
      );

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] Password changed successfully');
        return response.data;
      } else {
        throw new Error(response.data.message || 'Password change failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Password change failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Search user by username
   */
  async searchUser(username: string, token: string): Promise<any> {
    try {
      console.log('🔍 [KEYCLOAK-AUTH] Searching for user:', username);

      const response = await this.client.post(
        '/v2/hybrid-auth/search-user',
        { username },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        console.log('✅ [KEYCLOAK-AUTH] User found successfully:', username);
        return response.data;
      } else {
        throw new Error(response.data.message || 'User search failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] User search failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Update Keycloak credentials
   */
  async updateKeycloakCredentials(username: string, password: string, token: string): Promise<any> {
    try {
      console.log('🔄 [KEYCLOAK-AUTH] Updating Keycloak credentials for user:', username);

      const response = await this.client.post(
        '/v2/hybrid-auth/update-keycloak-credentials',
        {
          username,
          password,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        console.log(
          '✅ [KEYCLOAK-AUTH] Keycloak credentials updated successfully for user:',
          username
        );
        return response.data;
      } else {
        throw new Error(response.data.message || 'Keycloak credentials update failed');
      }
    } catch (error: any) {
      console.error(
        '❌ [KEYCLOAK-AUTH] Keycloak credentials update failed:',
        error.response?.data || error.message
      );
      throw this.handleError(error);
    }
  }

  /**
   * Validate token and return user information
   */
  async validateToken(token: string): Promise<{
    valid: boolean;
    user?: any;
    error?: string;
  }> {
    try {
      // Decode JWT token to get user information
      const payload = this.decodeJwtToken(token);
      if (!payload) {
        return {
          valid: false,
          error: 'Invalid token format',
        };
      }

      // Check if token is expired
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        return {
          valid: false,
          error: 'Token expired',
        };
      }

      // Validate token by making a test request
      const response = await this.client.post(
        '/v2/hybrid-auth/search-user',
        { username: 'test' },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const isValid = response.status === 200 || response.status === 404; // 404 is OK (user not found)

      if (isValid) {
        return {
          valid: true,
          user: payload,
        };
      } else {
        return {
          valid: false,
          error: 'Token validation failed',
        };
      }
    } catch (error: any) {
      if (error.response?.status === 401) {
        return {
          valid: false,
          error: 'Token is invalid',
        };
      }
      // For other errors, assume token might be valid but return the payload
      try {
        const payload = this.decodeJwtToken(token);
        return {
          valid: true,
          user: payload,
        };
      } catch {
        return {
          valid: false,
          error: 'Token validation failed',
        };
      }
    }
  }

  /**
   * Decode JWT token payload
   */
  private decodeJwtToken(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      const payload = parts[1];
      const decoded = Buffer.from(payload, 'base64').toString('utf8');
      return JSON.parse(decoded);
    } catch (error) {
      console.error('❌ [KEYCLOAK-AUTH] Failed to decode JWT token:', error);
      return null;
    }
  }

  /**
   * Handle and normalize errors
   */
  private handleError(error: any): Error {
    if (error.response?.data) {
      const errorData = error.response.data as KeycloakErrorResponse;
      return new Error(errorData.message || 'Authentication service error');
    }
    return error;
  }

  /**
   * Get service health status
   */
  async getHealthStatus(): Promise<boolean> {
    try {
      // Try to make a simple request to check if service is available
      await this.client.get('/health', { timeout: 5000 });
      return true;
    } catch (error) {
      console.warn('⚠️ [KEYCLOAK-AUTH] Service health check failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const keycloakAuthService = new KeycloakAuthService();
